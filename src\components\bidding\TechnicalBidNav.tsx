import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  Building,
  Lightbulb,
  Wrench,
  CalendarClock,
  BadgeCheck
} from 'lucide-react';
import Card from '../ui/Card';

const TechnicalBidNav: React.FC = () => {
  const navItems = [
    { path: '/technical', label: '技术标概览', icon: <LayoutDashboard size={18} /> },
    { path: '/technical/company-profile', label: '公司简介', icon: <Building size={18} /> },
    { path: '/technical/project-understanding', label: '项目理解', icon: <Lightbulb size={18} /> },
    { path: '/technical/solution', label: '技术方案', icon: <Wrench size={18} /> },
    { path: '/technical/implementation', label: '实施计划', icon: <CalendarClock size={18} /> },
    { path: '/technical/quality', label: '质量保证', icon: <BadgeCheck size={18} /> },
  ];

  return (
    <Card className="overflow-hidden p-0">
      <div className="bg-gray-50 px-4 py-3 border-b">
        <h3 className="text-sm font-medium text-gray-700">技术标章节</h3>
      </div>
      <nav className="p-2">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                end={item.path === '/technical'}
                className={({ isActive }) =>
                  `flex items-center px-3 py-2 text-sm rounded-md transition-colors
                  ${isActive 
                    ? 'bg-blue-50 text-blue-700 font-medium' 
                    : 'text-gray-700 hover:bg-gray-100'}`
                }
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </Card>
  );
};

export default TechnicalBidNav;