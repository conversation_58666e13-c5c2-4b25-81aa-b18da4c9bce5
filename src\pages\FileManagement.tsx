import React, { useState, useEffect } from 'react';
import { 
  Folder, 
  File, 
  Search, 
  Filter, 
  Download, 
  Trash2, 
  Eye, 
  Plus, 
  FileText, 
  FileArchive, 
  FileImage, 
  FileSpreadsheet, 
  RefreshCw,
  Upload as UploadIcon
} from 'lucide-react';
import { toast } from 'react-toastify';
import { fileApi } from '../services/api';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import UploadModal from '../components/files/UploadModal';
import { healthApi } from '../services/api';

interface FileData {
  id: string;
  name: string;
  type: string;
  size: number;
  category: string;
  uploadDate: string;
  filePath?: string;
  filename?: string;
}

// 文件类型图标映射
const fileIconMap: Record<string, React.ReactNode> = {
  'pdf': <FileText size={24} className="text-red-500" />,
  'doc': <FileText size={24} className="text-blue-500" />,
  'docx': <FileText size={24} className="text-blue-500" />,
  'xls': <FileSpreadsheet size={24} className="text-green-500" />,
  'xlsx': <FileSpreadsheet size={24} className="text-green-500" />,
  'ppt': <FileText size={24} className="text-orange-500" />,
  'pptx': <FileText size={24} className="text-orange-500" />,
  'txt': <FileText size={24} className="text-gray-500" />,
  'jpg': <FileImage size={24} className="text-purple-500" />,
  'jpeg': <FileImage size={24} className="text-purple-500" />,
  'png': <FileImage size={24} className="text-purple-500" />,
  'gif': <FileImage size={24} className="text-purple-500" />,
  'zip': <FileArchive size={24} className="text-orange-500" />,
  'rar': <FileArchive size={24} className="text-orange-500" />,
  'default': <File size={24} className="text-gray-500" />
};

const FileManagement: React.FC = () => {
  const [files, setFiles] = useState<FileData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('全部');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [serverStatus, setServerStatus] = useState<'unknown' | 'online' | 'offline'>('unknown');

  // 检查服务器状态
  const checkServerStatus = async () => {
    try {
      await healthApi.checkHealth();
      setServerStatus('online');
    } catch (error) {
      setServerStatus('offline');
      console.error('服务器离线:', error);
    }
  };

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      
      const response = await fileApi.getFiles(selectedCategory === '全部' ? undefined : selectedCategory);
      if (response.status === 200) {
        setFiles(Array.isArray(response.data) ? response.data : []);
        setServerStatus('online');
        console.log('文件列表加载成功:', response.data);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      setServerStatus('offline');
      toast.error('获取文件列表失败，请检查服务器连接 (端口3010)');
      setFiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchFiles();
    setIsRefreshing(false);
    toast.success('文件列表已刷新');
  };

  useEffect(() => {
    fetchFiles();
  }, [selectedCategory]);

  // 组件挂载时检查服务器状态
  useEffect(() => {
    checkServerStatus();
  }, []);

  const handleDeleteFile = async (fileId: string, fileName: string) => {
    if (!confirm(`确定要删除文件 "${fileName}" 吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const response = await fileApi.deleteFile(fileId);
      if (response.status === 200) {
        toast.success('文件删除成功');
        fetchFiles();
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      toast.error('删除文件失败，请稍后重试');
    }
  };

  const handleDownloadFile = async (fileId: string, fileName: string) => {
    try {
      await fileApi.downloadFile(fileId, fileName);
      toast.success('文件下载开始');
    } catch (error) {
      console.error('下载文件失败:', error);
      toast.error('下载文件失败，请稍后重试');
    }
  };

  const handlePreviewFile = async (fileId: string, fileName: string) => {
    try {
      const fileType = fileName.split('.').pop()?.toLowerCase();
      const previewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt'];
      
      if (!previewableTypes.includes(fileType || '')) {
        toast.info('该文件类型不支持预览，请下载后查看');
        return;
      }
      
      // 打开新窗口预览文件，使用相对路径通过Vite代理
      const previewUrl = `/api/files/${fileId}/preview`;
      window.open(previewUrl, '_blank');
    } catch (error) {
      console.error('预览文件失败:', error);
      toast.error('预览文件失败，请稍后重试');
    }
  };
  
  const categories = ['全部', '招标方文件', '投标方文件', '供应商文件'];
  
  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // 过滤文件
  const filteredFiles = Array.isArray(files) ? files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  }) : [];
  
  // 获取文件图标
  const getFileIcon = (fileType: string) => {
    return fileIconMap[fileType] || fileIconMap.default;
  };

  // 获取文件统计信息
  const getFileStats = () => {
    const totalFiles = Array.isArray(files) ? files.length : 0;
    const totalSize = Array.isArray(files) ? files.reduce((sum, file) => sum + file.size, 0) : 0;
    const categoryStats = categories.slice(1).map(category => ({
      category,
      count: Array.isArray(files) ? files.filter(file => file.category === category).length : 0
    }));
    
    return { totalFiles, totalSize, categoryStats };
  };

  const stats = getFileStats();

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900">文件管理</h1>
          <div className="flex items-center gap-2">
            <p className="text-gray-500">管理所有项目相关文件</p>
            <div className="flex items-center">
              <div className={`w-2 h-2 rounded-full mr-1 ${
                serverStatus === 'online' ? 'bg-green-500' : 
                serverStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
              }`}></div>
              <span className={`text-xs ${
                serverStatus === 'online' ? 'text-green-600' : 
                serverStatus === 'offline' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {serverStatus === 'online' ? '服务器在线' : 
                 serverStatus === 'offline' ? '服务器离线' : '检查中...'}
              </span>
            </div>
          </div>
        </div>
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw size={16} className={`mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button
            onClick={() => setShowUploadModal(true)}
          >
            <Plus size={16} className="mr-2" />
            上传文件
          </Button>
        </div>
      </div>

      {/* 文件统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <div className="p-2 rounded-full bg-blue-100 mr-3">
              <File size={20} className="text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">总文件数</p>
              <p className="text-xl font-bold text-gray-800">{stats.totalFiles}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <div className="p-2 rounded-full bg-green-100 mr-3">
              <UploadIcon size={20} className="text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">总大小</p>
              <p className="text-xl font-bold text-gray-800">{formatFileSize(stats.totalSize)}</p>
            </div>
          </div>
        </Card>

        {stats.categoryStats.slice(0, 2).map((stat, index) => (
          <Card key={stat.category} className="p-4">
            <div className="flex items-center">
              <div className={`p-2 rounded-full mr-3 ${
                index === 0 ? 'bg-purple-100' : 'bg-orange-100'
              }`}>
                <Folder size={20} className={
                  index === 0 ? 'text-purple-600' : 'text-orange-600'
                } />
              </div>
              <div>
                <p className="text-sm text-gray-500">{stat.category}</p>
                <p className="text-xl font-bold text-gray-800">{stat.count}</p>
              </div>
            </div>
          </Card>
        ))}
      </div>
      
      <Card>
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索文件..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="w-full md:w-48">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter size={18} className="text-gray-400" />
              </div>
              <select
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center">
              <RefreshCw size={20} className="animate-spin mr-2" />
              <span>加载中...</span>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              {serverStatus === 'offline' && (
                <caption className="py-4 text-center">
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-red-800 text-sm">
                      ⚠️ 后端服务器未运行，请执行 <code className="bg-red-100 px-1 rounded">npm run server</code> 启动服务器
                    </p>
                  </div>
                </caption>
              )}
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">文件名</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">大小</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上传日期</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFiles.map((file) => (
                  <tr key={file.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getFileIcon(file.type)}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900 max-w-xs truncate" title={file.name}>
                            {file.name}
                          </div>
                          <div className="text-xs text-gray-500">.{file.type}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                        {file.category}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatFileSize(file.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {file.uploadDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button 
                          className="text-gray-500 hover:text-blue-600 transition-colors"
                          onClick={() => handlePreviewFile(file.id, file.name)}
                          title="预览文件"
                        >
                          <Eye size={18} />
                        </button>
                        <button 
                          className="text-gray-500 hover:text-green-600 transition-colors"
                          onClick={() => handleDownloadFile(file.id, file.name)}
                          title="下载文件"
                        >
                          <Download size={18} />
                        </button>
                        <button 
                          className="text-gray-500 hover:text-red-600 transition-colors"
                          onClick={() => handleDeleteFile(file.id, file.name)}
                          title="删除文件"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            {filteredFiles.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <Folder className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  {searchTerm ? '未找到匹配的文件' : '暂无文件'}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? '请尝试其他搜索条件' : '开始上传文件或更改筛选条件'}
                </p>
                {!searchTerm && (
                  <div className="mt-6">
                    <Button
                      onClick={() => setShowUploadModal(true)}
                    >
                      <Plus size={16} className="mr-2" />
                      上传文件
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </Card>
      
      {showUploadModal && (
        <UploadModal 
          onClose={() => setShowUploadModal(false)}
          onUploadSuccess={fetchFiles}
        />
      )}
    </div>
  );
};

export default FileManagement;