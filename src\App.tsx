import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// 布局组件
import MainLayout from './layouts/MainLayout';
import ErrorBoundary from './components/ErrorBoundary';

// 页面组件
import Dashboard from './pages/Dashboard';
import TechnicalBid from './pages/TechnicalBid';
import CommercialBid from './pages/CommercialBid';
import FileManagement from './pages/FileManagement';
import FileManagementTest from './pages/FileManagementTest';
import FileUploadTest from './pages/FileUploadTest';
import SimpleTest from './pages/SimpleTest';
import Settings from './pages/Settings';
import NotFound from './pages/NotFound';
import Copyright from './pages/Copyright';
import Templates from './pages/Templates';
import BatchProcessing from './pages/BatchProcessing';

// 技术标子页面
import TechnicalOverview from './pages/technical/Overview';
import CompanyProfile from './pages/technical/CompanyProfile';
import ProjectUnderstanding from './pages/technical/ProjectUnderstanding';
import TechnicalSolution from './pages/technical/TechnicalSolution';
import ImplementationPlan from './pages/technical/ImplementationPlan';
import QualityAssurance from './pages/technical/QualityAssurance';

// 商务标子页面
import CommercialOverview from './pages/commercial/Overview';
import PricingStrategy from './pages/commercial/PricingStrategy';
import CostBreakdown from './pages/commercial/CostBreakdown';
import PaymentTerms from './pages/commercial/PaymentTerms';
import ContractConditions from './pages/commercial/ContractConditions';

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <ToastContainer position="top-right" autoClose={3000} />
        <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<Dashboard />} />
          
          {/* 技术标路由 */}
          <Route path="technical" element={<TechnicalBid />}>
            <Route index element={<TechnicalOverview />} />
            <Route path="company-profile" element={<CompanyProfile />} />
            <Route path="project-understanding" element={<ProjectUnderstanding />} />
            <Route path="solution" element={<TechnicalSolution />} />
            <Route path="implementation" element={<ImplementationPlan />} />
            <Route path="quality" element={<QualityAssurance />} />
          </Route>
          
          {/* 商务标路由 */}
          <Route path="commercial" element={<CommercialBid />}>
            <Route index element={<CommercialOverview />} />
            <Route path="pricing" element={<PricingStrategy />} />
            <Route path="costs" element={<CostBreakdown />} />
            <Route path="payment" element={<PaymentTerms />} />
            <Route path="contract" element={<ContractConditions />} />
          </Route>
          
          <Route path="files" element={<FileManagement />} />
          <Route path="files-test" element={<FileManagementTest />} />
          <Route path="upload-test" element={<FileUploadTest />} />
          <Route path="simple-test" element={<SimpleTest />} />
          <Route path="templates" element={<Templates />} />
          <Route path="batch" element={<BatchProcessing />} />
          <Route path="settings" element={<Settings />} />
          <Route path="copyright" element={<Copyright />} />
          <Route path="*" element={<NotFound />} />
        </Route>
        </Routes>
      </Router>
    </ErrorBoundary>
  );
}

export default App;