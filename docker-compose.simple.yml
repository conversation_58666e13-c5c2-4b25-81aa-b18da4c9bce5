services:
  # MongoDB数据库
  mongodb:
    image: mongo:6.0
    container_name: ai-toubiao-mongodb
    ports:
      - "27018:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=ai-toubiao
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.15-community
    container_name: ai-toubiao-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=1G
    volumes:
      - neo4j_data:/data
    restart: unless-stopped

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-toubiao-postgres
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=ai_toubiao
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  mongodb_data:
    driver: local
  neo4j_data:
    driver: local
  postgres_data:
    driver: local