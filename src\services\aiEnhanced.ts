import axios from 'axios';

// AI 提供商配置
interface AiProvider {
  name: string;
  url: string;
  apiKey: string;
  isDefault: boolean;
  model: string;
}

const aiProviders: AiProvider[] = [
  {
    name: 'deepseek',
    url: 'https://api.deepseek.com/v1',
    apiKey: '***********************************',
    isDefault: true,
    model: 'deepseek-chat'
  },
  {
    name: 'aliyun',
    url: 'https://dashscope.aliyuncs.com/api/v1',
    apiKey: 'sk-28ec47f5eff6404aa98329a9f56ada05',
    isDefault: false,
    model: 'qwen-turbo'
  }
];

class AIEnhancedService {
  private currentProvider: AiProvider;

  constructor() {
    this.currentProvider = aiProviders.find(p => p.isDefault) || aiProviders[0];
  }

  // 切换AI提供商
  switchProvider(providerName: string) {
    const provider = aiProviders.find(p => p.name === providerName);
    if (provider) {
      this.currentProvider = provider;
      return true;
    }
    return false;
  }

  // 分析招标文件并提取结构化信息
  async analyzeDocument(content: string): Promise<{
    success: boolean;
    data?: {
      projectInfo: any;
      requirements: any[];
      scoringCriteria: any[];
      technicalSpecs: any[];
      commercialTerms: any[];
      timeline: any;
      qualifications: any[];
    };
    error?: string;
  }> {
    try {
      const prompt = `
请分析以下招标文件内容，提取关键信息并以JSON格式返回：

文件内容：
${content}

请提取以下信息：
1. 项目基本信息（项目名称、招标单位、项目概述等）
2. 技术要求列表
3. 评分标准和权重
4. 技术规格要求
5. 商务条款
6. 项目时间安排
7. 投标人资质要求

返回格式：
{
  "projectInfo": {
    "name": "项目名称",
    "client": "招标单位",
    "overview": "项目概述",
    "budget": "预算范围"
  },
  "requirements": [
    {
      "id": "req_001",
      "category": "技术要求",
      "content": "具体要求内容",
      "priority": "高/中/低",
      "mandatory": true/false
    }
  ],
  "scoringCriteria": [
    {
      "id": "score_001",
      "category": "技术标",
      "content": "评分内容",
      "weight": 60,
      "maxScore": 100
    }
  ],
  "technicalSpecs": [
    {
      "id": "tech_001",
      "category": "系统架构",
      "specification": "具体技术规格",
      "parameters": {}
    }
  ],
  "commercialTerms": [
    {
      "id": "comm_001",
      "type": "付款方式",
      "content": "具体条款内容"
    }
  ],
  "timeline": {
    "bidSubmission": "投标截止时间",
    "projectStart": "项目开始时间",
    "projectEnd": "项目结束时间",
    "milestones": []
  },
  "qualifications": [
    {
      "id": "qual_001",
      "type": "资质要求",
      "content": "具体资质要求",
      "required": true/false
    }
  ]
}
`;

      const response = await this.callAI(prompt);
      
      if (response.success && response.data) {
        try {
          const parsedData = JSON.parse(response.data);
          return {
            success: true,
            data: parsedData
          };
        } catch (parseError) {
          console.error('解析AI响应失败:', parseError);
          return {
            success: false,
            error: '解析AI响应失败'
          };
        }
      }
      
      return response;
    } catch (error) {
      console.error('文档分析失败:', error);
      return {
        success: false,
        error: '文档分析失败'
      };
    }
  }

  // 生成技术标内容
  async generateTechnicalContent(section: string, requirements: any[], context: any): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      let prompt = '';
      
      switch (section) {
        case 'companyProfile':
          prompt = `
根据以下招标要求，生成专业的公司简介内容：

招标要求：
${JSON.stringify(requirements, null, 2)}

公司背景信息：
${JSON.stringify(context, null, 2)}

请生成包含以下内容的公司简介：
1. 公司基本信息
2. 相关资质证书
3. 技术实力展示
4. 成功案例介绍
5. 团队优势

要求：
- 内容专业、准确
- 突出与项目相关的优势
- 符合招标文件要求
- 字数控制在1000-1500字
`;
          break;
          
        case 'technicalSolution':
          prompt = `
根据以下技术要求，设计详细的技术解决方案：

技术要求：
${JSON.stringify(requirements, null, 2)}

项目背景：
${JSON.stringify(context, null, 2)}

请生成包含以下内容的技术方案：
1. 总体架构设计
2. 技术选型说明
3. 功能模块设计
4. 性能保障措施
5. 安全保障方案
6. 扩展性设计

要求：
- 技术方案可行性强
- 充分响应招标要求
- 体现技术创新点
- 包含必要的技术图表说明
`;
          break;
          
        case 'implementationPlan':
          prompt = `
根据项目要求制定详细的实施计划：

项目要求：
${JSON.stringify(requirements, null, 2)}

项目信息：
${JSON.stringify(context, null, 2)}

请生成包含以下内容的实施计划：
1. 项目组织架构
2. 人员配置方案
3. 详细进度安排
4. 里程碑设置
5. 风险管理措施
6. 质量控制计划

要求：
- 计划详细可执行
- 时间安排合理
- 风险考虑充分
- 符合项目实际情况
`;
          break;
          
        default:
          prompt = `
根据招标要求生成${section}相关内容：

要求：
${JSON.stringify(requirements, null, 2)}

背景信息：
${JSON.stringify(context, null, 2)}

请生成专业、详细的内容，确保充分响应招标要求。
`;
      }

      return await this.callAI(prompt);
    } catch (error) {
      console.error('生成技术标内容失败:', error);
      return {
        success: false,
        error: '生成技术标内容失败'
      };
    }
  }

  // 生成商务标内容
  async generateCommercialContent(section: string, requirements: any[], context: any): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      let prompt = '';
      
      switch (section) {
        case 'pricingStrategy':
          prompt = `
根据项目要求制定合理的报价策略：

项目要求：
${JSON.stringify(requirements, null, 2)}

市场信息：
${JSON.stringify(context, null, 2)}

请生成包含以下内容的报价策略：
1. 总体报价思路
2. 竞争分析
3. 价格优势说明
4. 成本控制措施
5. 风险定价考虑

要求：
- 报价合理有竞争力
- 分析充分有说服力
- 体现价格优势
`;
          break;
          
        case 'costBreakdown':
          prompt = `
根据项目要求制定详细的成本分解：

项目要求：
${JSON.stringify(requirements, null, 2)}

成本信息：
${JSON.stringify(context, null, 2)}

请生成详细的成本明细表，包括：
1. 人工成本
2. 设备成本
3. 软件成本
4. 服务成本
5. 其他费用

要求：
- 成本分解详细准确
- 计算逻辑清晰
- 符合行业标准
`;
          break;
          
        default:
          prompt = `
根据商务要求生成${section}相关内容：

要求：
${JSON.stringify(requirements, null, 2)}

背景信息：
${JSON.stringify(context, null, 2)}

请生成专业、详细的商务内容。
`;
      }

      return await this.callAI(prompt);
    } catch (error) {
      console.error('生成商务标内容失败:', error);
      return {
        success: false,
        error: '生成商务标内容失败'
      };
    }
  }

  // 检查投标文件合规性
  async checkCompliance(bidContent: any, requirements: any[]): Promise<{
    success: boolean;
    data?: {
      overallScore: number;
      issues: any[];
      suggestions: any[];
      missingItems: any[];
    };
    error?: string;
  }> {
    try {
      const prompt = `
请检查以下投标文件内容是否符合招标要求：

招标要求：
${JSON.stringify(requirements, null, 2)}

投标文件内容：
${JSON.stringify(bidContent, null, 2)}

请从以下方面进行检查并返回JSON格式结果：
1. 整体符合度评分（0-100分）
2. 发现的问题列表
3. 改进建议
4. 缺失的必要内容

返回格式：
{
  "overallScore": 85,
  "issues": [
    {
      "section": "技术方案",
      "issue": "缺少具体的技术架构图",
      "severity": "高/中/低"
    }
  ],
  "suggestions": [
    {
      "section": "实施计划",
      "suggestion": "建议增加详细的时间节点",
      "priority": "高/中/低"
    }
  ],
  "missingItems": [
    {
      "requirement": "必须提供的资质证书",
      "section": "公司简介",
      "mandatory": true
    }
  ]
}
`;

      const response = await this.callAI(prompt);
      
      if (response.success && response.data) {
        try {
          const parsedData = JSON.parse(response.data);
          return {
            success: true,
            data: parsedData
          };
        } catch (parseError) {
          return {
            success: false,
            error: '解析合规检查结果失败'
          };
        }
      }
      
      return response;
    } catch (error) {
      console.error('合规检查失败:', error);
      return {
        success: false,
        error: '合规检查失败'
      };
    }
  }

  // 优化内容响应度
  async optimizeResponseRelevance(content: string, requirements: any[]): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      const prompt = `
请优化以下内容，提高其对招标要求的响应度：

原始内容：
${content}

招标要求：
${JSON.stringify(requirements, null, 2)}

优化要求：
1. 确保内容充分响应招标要求
2. 突出关键优势和亮点
3. 增强内容的说服力
4. 保持专业性和准确性
5. 适当增加具体数据和案例

请返回优化后的内容：
`;

      return await this.callAI(prompt);
    } catch (error) {
      console.error('内容优化失败:', error);
      return {
        success: false,
        error: '内容优化失败'
      };
    }
  }

  // 调用AI接口
  private async callAI(prompt: string): Promise<{
    success: boolean;
    data?: string;
    error?: string;
  }> {
    try {
      const response = await axios.post(
        `${this.currentProvider.url}/chat/completions`,
        {
          model: this.currentProvider.model,
          messages: [
            {
              role: 'system',
              content: '你是一个专业的投标文件制作助手，擅长分析招标文件和生成高质量的投标内容。'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 4000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.currentProvider.apiKey}`
          },
          timeout: 30000
        }
      );

      return {
        success: true,
        data: response.data.choices[0].message.content
      };
    } catch (error: any) {
      console.error(`AI调用失败 (${this.currentProvider.name}):`, error);
      
      // 尝试切换到备用提供商
      if (this.currentProvider.isDefault && aiProviders.length > 1) {
        const backupProvider = aiProviders.find(p => !p.isDefault);
        if (backupProvider) {
          console.log(`切换到备用AI提供商: ${backupProvider.name}`);
          this.currentProvider = backupProvider;
          return this.callAI(prompt);
        }
      }
      
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'AI调用失败'
      };
    }
  }
}

export const aiEnhancedService = new AIEnhancedService();
export default aiEnhancedService;