import mongoose from 'mongoose';

// 数据库配置
const DB_CONFIG = {
  // 默认使用本地MongoDB
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/bidding_platform',
  
  // 连接选项
  options: {
    // 连接池设置
    maxPoolSize: 10,
    
    // 超时设置
    serverSelectionTimeoutMS: 5000,
    socketTimeoutMS: 45000,
    
    // 其他设置
    retryWrites: true
  }
};

// 连接数据库
export const connectDatabase = async () => {
  try {
    console.log('正在连接MongoDB数据库...');
    console.log('连接字符串:', DB_CONFIG.MONGODB_URI);
    
    await mongoose.connect(DB_CONFIG.MONGODB_URI, DB_CONFIG.options);
    
    console.log('✅ MongoDB数据库连接成功');
    console.log('数据库名称:', mongoose.connection.db.databaseName);
    
    // 监听连接事件
    mongoose.connection.on('error', (error) => {
      console.error('❌ MongoDB连接错误:', error);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️  MongoDB连接断开');
    });
    
    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB重新连接成功');
    });
    
    return mongoose.connection;
  } catch (error) {
    console.error('❌ MongoDB数据库连接失败:', error);
    console.log('📋 请确保MongoDB服务正在运行...');
    console.log('💡 如果没有安装MongoDB，可以：');
    console.log('   1. 安装本地MongoDB: https://www.mongodb.com/try/download/community');
    console.log('   2. 使用MongoDB Atlas云服务: https://www.mongodb.com/atlas');
    console.log('   3. 使用Docker启动: docker run -d -p 27017:27017 mongo');
    
    // 继续使用JSON文件存储，不中断应用运行
    console.log('⚠️  将继续使用JSON文件存储，功能受限');
    return null;
  }
};

// 断开数据库连接
export const disconnectDatabase = async () => {
  try {
    await mongoose.disconnect();
    console.log('MongoDB数据库连接已断开');
  } catch (error) {
    console.error('断开数据库连接时出错:', error);
  }
};

// 检查数据库连接状态
export const checkDatabaseConnection = () => {
  const state = mongoose.connection.readyState;
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  
  return {
    state,
    status: states[state],
    isConnected: state === 1
  };
};

// 清理数据库连接
process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

export default {
  connectDatabase,
  disconnectDatabase,
  checkDatabaseConnection,
  config: DB_CONFIG
};