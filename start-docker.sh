#!/bin/bash

# AI投标系统Docker启动脚本
echo "🚀 启动AI投标文件制作平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads data logs docker/nginx/ssl

# 设置权限
echo "🔒 设置目录权限..."
chmod 755 uploads data logs
chmod -R 755 docker/

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 清理旧的镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
    docker-compose build --no-cache
fi

# 启动服务
echo "🎯 启动所有服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示服务信息
echo ""
echo "✅ 服务启动完成！"
echo ""
echo "🌐 访问地址："
echo "  主应用(Nginx): http://localhost:8080"
echo "  主应用(直接): http://localhost:3010"
echo "  API健康检查: http://localhost:3010/api/health"
echo ""
echo "🗄️ 数据库访问："
echo "  MongoDB: localhost:27020 (admin/password123)"
echo "  Neo4j: http://localhost:7480 (neo4j/password123)"
echo "  PostgreSQL: localhost:5435 (postgres/password123)"
echo "  Redis: localhost:6385 (password123)"
echo ""
echo "📱 管理工具："
echo "  Neo4j Browser: http://localhost:7480"
echo ""
echo "📋 常用命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo "  进入容器: docker-compose exec app sh"
echo ""

# 健康检查
echo "🔍 执行健康检查..."
sleep 5

if curl -f http://localhost/api/health > /dev/null 2>&1; then
    echo "✅ 应用健康检查通过"
else
    echo "⚠️ 应用健康检查失败，请检查日志："
    echo "   docker-compose logs app"
fi

echo ""
echo "🎉 AI投标文件制作平台已启动完成！"