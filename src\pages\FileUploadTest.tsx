import React, { useState } from 'react';
import { toast } from 'react-toastify';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { Upload } from 'lucide-react';

const FileUploadTest: React.FC = () => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setSelectedFiles(Array.from(e.target.files));
    }
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      toast.error('请选择文件');
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      console.log('开始上传文件...');
      
      // 测试后端连接
      const healthResponse = await fetch('/api/health');
      console.log('健康检查:', healthResponse.status);
      
      if (!healthResponse.ok) {
        throw new Error('后端服务器连接失败');
      }

      // 创建FormData
      const formData = new FormData();
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });
      formData.append('category', 'other');

      console.log('发送上传请求...');
      
      // 上传文件
      const uploadResponse = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData
      });

      console.log('上传响应:', uploadResponse.status);

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.message || '上传失败');
      }

      const result = await uploadResponse.json();
      console.log('上传成功:', result);
      
      setUploadResult(result);
      toast.success(`成功上传 ${selectedFiles.length} 个文件`);
      setSelectedFiles([]);
      
      // 重置文件输入
      const fileInput = document.getElementById('file-input') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }

    } catch (error: any) {
      console.error('上传失败:', error);
      toast.error(`上传失败: ${error.message}`);
      setUploadResult({ error: error.message });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card>
        <h2 className="text-2xl font-bold mb-6">文件上传测试</h2>
        
        <div className="space-y-6">
          {/* 文件选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择文件
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <input
                id="file-input"
                type="file"
                multiple
                onChange={handleFileChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                disabled={isUploading}
              />
            </div>
          </div>

          {/* 已选择的文件 */}
          {selectedFiles.length > 0 && (
            <div>
              <h3 className="text-lg font-medium mb-3">已选择的文件 ({selectedFiles.length})</h3>
              <div className="space-y-2">
                {selectedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                    <div>
                      <span className="font-medium">{file.name}</span>
                      <span className="text-sm text-gray-500 ml-2">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">{file.type}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 上传按钮 */}
          <div>
            <Button
              onClick={handleUpload}
              disabled={selectedFiles.length === 0 || isUploading}
              className="w-full"
            >
              {isUploading ? '上传中...' : `上传文件 (${selectedFiles.length})`}
            </Button>
          </div>

          {/* 上传结果 */}
          {uploadResult && (
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-3">上传结果</h3>
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-auto">
                {JSON.stringify(uploadResult, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default FileUploadTest;
