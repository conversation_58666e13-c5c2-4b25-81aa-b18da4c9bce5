import React from 'react';
import { FileText, Code, Shield, Mail, Phone } from 'lucide-react';
import Card from '../components/ui/Card';

const Copyright: React.FC = () => {
  return (
    <div className="max-w-3xl mx-auto space-y-6">
      <Card>
        <div className="flex items-center justify-center mb-6">
          <div className="flex items-center space-x-4">
            {/* Logo 占位区域 */}
            <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <FileText size={24} className="text-gray-400 mx-auto mb-1" />
                <span className="text-xs text-gray-500">Logo</span>
              </div>
            </div>
            <div>
              <span className="font-bold text-2xl text-gray-800">标书制作平台</span>
              <p className="text-sm text-gray-500">Professional Bidding Platform</p>
            </div>
          </div>
        </div>
        
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">版权信息</h1>
          <p className="text-gray-500">版本号: v1.0.0</p>
        </div>
        
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
              <Code size={20} className="mr-2 text-blue-600" />
              平台介绍
            </h2>
            <div className="bg-gray-50 rounded-md p-4">
              <p className="text-gray-700 mb-3">本平台是专为项目投标而设计的智能标书制作系统，集成了先进的AI技术和多数据库架构，为用户提供高效、智能的标书制作解决方案。</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div className="bg-white rounded-md p-3">
                  <h4 className="font-medium text-gray-800 mb-2">核心功能</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• AI智能文档分析</li>
                    <li>• 自动内容生成</li>
                    <li>• 合规性检查</li>
                    <li>• 多格式文件支持</li>
                  </ul>
                </div>
                
                <div className="bg-white rounded-md p-3">
                  <h4 className="font-medium text-gray-800 mb-2">技术架构</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• React + TypeScript</li>
                    <li>• MongoDB + Neo4j + PostgreSQL</li>
                    <li>• DeepSeek AI + 阿里云AI</li>
                    <li>• Docker 容器化部署</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
              <Shield size={20} className="mr-2 text-blue-600" />
              版权声明
            </h2>
            <div className="bg-gray-50 rounded-md p-4">
              <p className="text-gray-700 mb-2">© 2025 标书制作平台 版权所有</p>
              <p className="text-gray-700 mb-2">本软件及其所有内容受知识产权法律保护，未经许可，不得进行商业用途的复制、修改或分发。</p>
              <p className="text-gray-700 mb-2">系统集成的第三方技术和服务，其版权归属于各自所有者。</p>
              
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  <strong>开源协议:</strong> MIT License<br/>
                  <strong>版本信息:</strong> v1.0.0 (Build 20250101)<br/>
                  <strong>最后更新:</strong> 2025年1月1日
                </p>
              </div>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">技术支持</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-md p-4 flex items-center">
                <Mail size={20} className="mr-3 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-500">电子邮件</p>
                  <p className="text-gray-700"><EMAIL></p>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-md p-4 flex items-center">
                <Phone size={20} className="mr-3 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-500">服务热线</p>
                  <p className="text-gray-700">************</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-8 pt-6 border-t text-center text-sm text-gray-500">
          <p>感谢您使用标书制作平台，我们致力于为您提供更优质的服务！</p>
        </div>
      </Card>
    </div>
  );
};

export default Copyright;