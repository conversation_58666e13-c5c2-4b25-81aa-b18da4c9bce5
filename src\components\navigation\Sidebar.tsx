import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, FileText, DollarSign, FolderOpen, Settings, ChevronDown, ChevronRight, Shield, Building, Lightbulb, Wrench, CalendarClock, BadgeCheck, Tags, Calculator, CreditCard, Contact as FileContract, Info, Layers, Zap } from 'lucide-react';
import Logo from '../common/Logo';

interface SidebarProps {
  isOpen: boolean;
}

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  label: string;
  hasChildren?: boolean;
  isOpen?: boolean;
  onClick?: () => void;
}

interface SubNavItemProps {
  to: string;
  label: string;
}

const NavItem: React.FC<NavItemProps> = ({ to, icon, label, hasChildren, isOpen, onClick }) => {
  return (
    <div className="relative">
      <NavLink
        to={to}
        end={to === '/' || !hasChildren}
        className={({ isActive }) => 
          `flex items-center px-4 py-3 text-sm font-medium transition-colors duration-200 
          ${isActive ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-100'}`
        }
        onClick={onClick}
      >
        <span className="mr-3">{icon}</span>
        <span className="flex-1">{label}</span>
        {hasChildren && (
          <span className="ml-auto">
            {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
          </span>
        )}
      </NavLink>
    </div>
  );
};

const SubNavItem: React.FC<SubNavItemProps> = ({ to, label }) => {
  return (
    <NavLink
      to={to}
      className={({ isActive }) => 
        `flex items-center pl-10 py-2 text-sm transition-colors duration-200
        ${isActive ? 'text-blue-600 bg-blue-50' : 'text-gray-600 hover:bg-gray-100'}`
      }
    >
      {label}
    </NavLink>
  );
};

const Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {
  const [technicalOpen, setTechnicalOpen] = React.useState(false);
  const [commercialOpen, setCommercialOpen] = React.useState(false);

  if (!isOpen) return null;

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Logo 区域 */}
      <div className="flex items-center justify-center h-16 border-b">
        <Logo />
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1">
          <li>
            <NavItem to="/" icon={<Home size={18} />} label="首页" />
          </li>
          
          {/* 技术标 */}
          <li>
            <NavItem 
              to="/technical" 
              icon={<FileText size={18} />} 
              label="技术标制作" 
              hasChildren={true} 
              isOpen={technicalOpen}
              onClick={() => setTechnicalOpen(!technicalOpen)}
            />
            
            {technicalOpen && (
              <div className="overflow-hidden">
                <SubNavItem to="/technical" label="技术标概览" />
                <SubNavItem to="/technical/company-profile" label="公司简介" />
                <SubNavItem to="/technical/project-understanding" label="项目理解" />
                <SubNavItem to="/technical/solution" label="技术方案" />
                <SubNavItem to="/technical/implementation" label="实施计划" />
                <SubNavItem to="/technical/quality" label="质量保证" />
              </div>
            )}
          </li>
          
          {/* 商务标 */}
          <li>
            <NavItem 
              to="/commercial" 
              icon={<DollarSign size={18} />} 
              label="商务标制作" 
              hasChildren={true} 
              isOpen={commercialOpen}
              onClick={() => setCommercialOpen(!commercialOpen)}
            />
            
            {commercialOpen && (
              <div className="overflow-hidden">
                <SubNavItem to="/commercial" label="商务标概览" />
                <SubNavItem to="/commercial/pricing" label="报价策略" />
                <SubNavItem to="/commercial/costs" label="成本明细" />
                <SubNavItem to="/commercial/payment" label="付款条件" />
                <SubNavItem to="/commercial/contract" label="合同条款" />
              </div>
            )}
          </li>
          
          <li>
            <NavItem to="/files" icon={<FolderOpen size={18} />} label="文件管理" />
          </li>
          
          <li>
            <NavItem to="/templates" icon={<Layers size={18} />} label="模板库" />
          </li>
          
          <li>
            <NavItem to="/batch" icon={<Zap size={18} />} label="批量处理" />
          </li>
          
          <li>
            <NavItem to="/settings" icon={<Settings size={18} />} label="系统设置" />
          </li>
        </ul>
      </nav>

      {/* 底部版权信息 */}
      <div className="py-2 px-4 border-t">
        <NavLink 
          to="/copyright" 
          className="flex items-center text-xs text-gray-500 hover:text-gray-700"
        >
          <Info size={14} className="mr-2" />
          <span>版权信息</span>
        </NavLink>
      </div>
    </div>
  );
};

export default Sidebar;