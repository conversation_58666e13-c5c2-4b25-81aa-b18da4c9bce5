# 端口配置说明

## 📋 端口配置总览

### 🔧 开发环境

| 服务 | 端口 | 说明 | 访问地址 |
|------|------|------|----------|
| Vite开发服务器 | 5173 | 前端热重载开发服务器 | http://localhost:5173 |
| Express API | 3008 | 后端API服务器 | http://localhost:3008 |

**开发环境API代理配置：**
- Vite代理：`/api` -> `http://localhost:3008/api`

### 🐳 生产环境 (Docker)

#### 应用服务端口

| 服务 | 容器内端口 | 宿主机端口 | 说明 | 访问地址 |
|------|------------|------------|------|----------|
| 应用容器 | 3008 | 3010 | Express服务器(前端+API) | http://localhost:3010 |
| Nginx | 80 | 8080 | 反向代理服务器 | http://localhost:8080 |
| Nginx SSL | 443 | 8443 | HTTPS反向代理 | https://localhost:8443 |

#### 数据库服务端口

| 服务 | 容器内端口 | 宿主机端口 | 说明 | 访问地址 |
|------|------------|------------|------|----------|
| MongoDB | 27017 | 27020 | 文档数据库 | mongodb://localhost:27020 |
| Neo4j HTTP | 7474 | 7480 | 图数据库Web界面 | http://localhost:7480 |
| Neo4j Bolt | 7687 | 7690 | 图数据库连接协议 | bolt://localhost:7690 |
| PostgreSQL | 5432 | 5435 | 关系型数据库 | postgresql://localhost:5435 |
| Redis | 6379 | 6385 | 缓存数据库 | redis://localhost:6385 |

## 🎯 推荐访问方式

### 主要访问入口
1. **推荐**: http://localhost:8080 (通过Nginx，支持负载均衡和缓存)
2. **直接**: http://localhost:3010 (直接访问应用容器)

### API访问
- **健康检查**: http://localhost:3010/api/health
- **文件上传**: http://localhost:3010/api/files/upload
- **AI聊天**: http://localhost:3010/api/ai/chat

### 数据库管理
- **Neo4j Browser**: http://localhost:7480 (用户名: neo4j, 密码: password123)
- **MongoDB**: 使用MongoDB Compass连接 ***********************************************************************

## 🔧 配置文件说明

### 开发环境配置
- `vite.config.ts`: 配置前端开发服务器和API代理
- `server.js`: 配置后端Express服务器端口

### 生产环境配置
- `docker-compose.yml`: 配置所有服务的端口映射
- `docker/nginx/nginx.conf`: 配置Nginx反向代理
- `.env.docker`: 配置容器内环境变量

## 🚨 端口冲突解决

如果遇到端口冲突，可以修改以下文件：

1. **修改应用端口**:
   - 编辑 `docker-compose.yml` 中的 `ports` 配置
   - 例如：将 `"3010:3008"` 改为 `"3011:3008"`

2. **修改数据库端口**:
   - 编辑对应服务的端口映射
   - 例如：将MongoDB的 `"27020:27017"` 改为 `"27021:27017"`

3. **修改Nginx端口**:
   - 编辑nginx服务的 `"8080:80"` 配置
   - 例如：改为 `"8081:80"`

## 📝 开发注意事项

1. **开发环境**: 前端和后端分别启动，通过Vite代理访问API
2. **生产环境**: 前端构建后由Express提供静态文件服务
3. **容器通信**: 容器间通过服务名通信，不使用localhost
4. **健康检查**: 应用容器内置健康检查，监听3008端口

## 🔍 故障排查

### 检查端口占用
```bash
# Windows
netstat -ano | findstr :3010

# Linux/Mac
lsof -i :3010
```

### 检查容器状态
```bash
docker-compose ps
docker-compose logs app
```

### 测试连接
```bash
# 测试应用健康状态
curl http://localhost:3010/api/health

# 测试Nginx代理
curl http://localhost:8080/api/health
```
