import React from 'react';
import { Menu, Bell, User, ChevronLeft, ChevronRight } from 'lucide-react';
import { useLocation } from 'react-router-dom';

interface HeaderProps {
  toggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {
  const location = useLocation();
  
  // 根据路径获取页面标题
  const getPageTitle = () => {
    const path = location.pathname;
    
    if (path === '/') return '首页';
    if (path.includes('/technical')) {
      if (path === '/technical') return '技术标制作 - 概览';
      if (path.includes('/company-profile')) return '技术标制作 - 公司简介';
      if (path.includes('/project-understanding')) return '技术标制作 - 项目理解';
      if (path.includes('/solution')) return '技术标制作 - 技术方案';
      if (path.includes('/implementation')) return '技术标制作 - 实施计划';
      if (path.includes('/quality')) return '技术标制作 - 质量保证';
    }
    if (path.includes('/commercial')) {
      if (path === '/commercial') return '商务标制作 - 概览';
      if (path.includes('/pricing')) return '商务标制作 - 报价策略';
      if (path.includes('/costs')) return '商务标制作 - 成本明细';
      if (path.includes('/payment')) return '商务标制作 - 付款条件';
      if (path.includes('/contract')) return '商务标制作 - 合同条款';
    }
    if (path === '/files') return '文件管理';
    if (path === '/settings') return '系统设置';
    if (path === '/copyright') return '版权信息';
    
    return '标书制作平台';
  };

  return (
    <header className="bg-white border-b h-16 px-6 flex items-center justify-between shadow-sm z-10">
      <div className="flex items-center">
        <button 
          onClick={toggleSidebar}
          className="p-2 rounded-md text-gray-500 hover:bg-gray-100 hover:text-gray-700 transition-colors duration-200"
        >
          {sidebarOpen ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
        </button>
        <h1 className="ml-4 text-xl font-medium text-gray-800">{getPageTitle()}</h1>
      </div>
      
      <div className="flex items-center space-x-3">
        <button className="p-2 rounded-full text-gray-500 hover:bg-gray-100 hover:text-gray-700 transition-colors duration-200">
          <Bell size={20} />
        </button>
        <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
          <User size={18} />
        </div>
      </div>
    </header>
  );
};

export default Header;