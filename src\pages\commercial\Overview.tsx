import React, { useState } from 'react';
import { ArrowRight, FileText, Upload, Bot, DollarSign } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const CommercialOverview: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);
  
  const completionStatus = [
    { section: '报价策略', completion: 15 },
    { section: '成本明细', completion: 10 },
    { section: '付款条件', completion: 5 },
    { section: '合同条款', completion: 0 },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">商务标制作概览</h2>
            <p className="text-gray-600 mb-6">
              商务标是投标文件的重要组成部分，主要包括投标报价、商务条款等内容。
              合理的商务标能够增加中标的可能性，同时也需要保证企业的合理利润。
              请根据以下步骤，制作专业、竞争力强的商务标文件。
            </p>
            
            <div className="space-y-4">
              {completionStatus.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-700">{item.section}</span>
                    <span className="text-sm text-gray-500">{item.completion}%</span>
                  </div>
                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-green-600 rounded-full" 
                      style={{ width: `${item.completion}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 flex flex-wrap gap-3">
              <Button 
                variant="primary"
                onClick={() => setShowAiChat(true)}
              >
                <Bot size={16} className="mr-2" />
                AI辅助编写
              </Button>
              <Button variant="outline">
                <FileText size={16} className="mr-2" />
                预览文档
              </Button>
            </div>
          </div>
          
          <div className="w-full md:w-64 shrink-0">
            <Card className="bg-gray-50 border-dashed">
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <DollarSign className="h-10 w-10 text-gray-400" />
                </div>
                <h3 className="text-sm font-medium text-gray-700">商务参考信息</h3>
                <p className="text-xs text-gray-500">
                  上传预算表或历史报价信息，AI将辅助制定合理报价策略
                </p>
                <Button size="sm" fullWidth>上传文件</Button>
              </div>
            </Card>
          </div>
        </div>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold text-gray-800 mb-4">商务标制作流程</h2>
        
        <div className="space-y-6">
          <div className="relative pb-6 pl-6 border-l-2 border-green-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-green-600"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">1. 分析招标文件商务要求</h3>
            <p className="text-sm text-gray-600">
              仔细分析招标文件中的商务要求，包括报价方式、付款条件、合同条款等。
            </p>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-green-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-green-500"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">2. 制定报价策略</h3>
            <p className="text-sm text-gray-600">
              基于市场行情、竞争对手情况、公司成本等因素，制定合理的报价策略。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-green-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-green-400"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">3. 编制成本明细</h3>
            <p className="text-sm text-gray-600">
              详细列出项目各项成本，包括人工成本、设备成本、服务成本等。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-green-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-green-300"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">4. 设定付款条件</h3>
            <p className="text-sm text-gray-600">
              根据企业资金情况和项目特点，设定合理的付款条件和方式。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pl-6">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-green-200"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">5. 拟定合同条款</h3>
            <p className="text-sm text-gray-600">
              对招标文件中的合同条款进行响应，并提出有利于企业的合理化建议。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="商务标制作" 
        />
      )}
    </div>
  );
};

export default CommercialOverview;