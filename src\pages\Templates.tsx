import React from 'react';
import TemplateLibrary from '../components/templates/TemplateLibrary';

const Templates: React.FC = () => {
  const handleSelectTemplate = (template: any) => {
    console.log('选择模板:', template);
  };

  const handleCreateTemplate = () => {
    console.log('创建新模板');
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">模板库</h1>
        <p className="text-gray-500">使用专业模板快速开始标书制作</p>
      </div>
      
      <TemplateLibrary
        onSelectTemplate={handleSelectTemplate}
        onCreateTemplate={handleCreateTemplate}
      />
    </div>
  );
};

export default Templates;