import React, { useState, useRef, useEffect } from 'react';
import { X, Send, Bot, Loader2, FileText, CheckCircle, AlertTriangle, Lightbulb } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../ui/Button';
import { v4 as uuidv4 } from 'uuid';
import ReactMarkdown from 'react-markdown';
import { aiEnhancedService } from '../../services/aiEnhanced';

interface EnhancedAiChatProps {
  onClose: () => void;
  context: string;
  documentData?: any;
  currentContent?: string;
  onContentGenerated?: (content: string) => void;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'analysis' | 'generation' | 'compliance';
  data?: any;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
  description: string;
}

const EnhancedAiChat: React.FC<EnhancedAiChatProps> = ({ 
  onClose, 
  context, 
  documentData,
  currentContent,
  onContentGenerated 
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'analysis' | 'compliance'>('chat');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 快捷操作
  const quickActions: QuickAction[] = [
    {
      id: 'analyze',
      label: '分析文档',
      icon: <FileText size={16} />,
      description: '分析招标文件并提取关键信息',
      action: () => handleAnalyzeDocument()
    },
    {
      id: 'generate',
      label: '生成内容',
      icon: <Bot size={16} />,
      description: '根据要求生成标书内容',
      action: () => handleGenerateContent()
    },
    {
      id: 'check',
      label: '合规检查',
      icon: <CheckCircle size={16} />,
      description: '检查内容是否符合招标要求',
      action: () => handleComplianceCheck()
    },
    {
      id: 'optimize',
      label: '优化内容',
      icon: <Lightbulb size={16} />,
      description: '优化内容的响应度和质量',
      action: () => handleOptimizeContent()
    }
  ];

  useEffect(() => {
    // 添加初始AI消息
    const initialMessage: Message = {
      id: uuidv4(),
      content: `您好！我是您的AI助手，专门协助${context}工作。我可以帮您：

📄 **分析招标文件** - 提取关键要求和评分标准
🤖 **智能生成内容** - 根据要求生成专业的标书内容  
✅ **合规性检查** - 确保内容符合招标要求
💡 **内容优化** - 提升内容质量和响应度

请选择您需要的功能，或直接向我提问！`,
      sender: 'ai',
      timestamp: new Date(),
      type: 'text'
    };
    setMessages([initialMessage]);
  }, [context]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim()) return;
    
    const userMessage: Message = {
      id: uuidv4(),
      content: input,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    
    try {
      // 调用真实的AI API
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: input,
          context: context
        }),
      });
      
      const data = await response.json();
      
      const aiResponse: Message = {
        id: uuidv4(),
        content: data.success ? data.message : data.message || '处理失败',
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };
      
      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      const errorMessage: Message = {
        id: uuidv4(),
        content: '抱歉，AI服务暂时不可用，请稍后再试。',
        sender: 'ai',
        timestamp: new Date(),
        type: 'text'
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyzeDocument = async () => {
    if (!documentData) {
      addMessage('请先上传招标文件，我才能进行分析。', 'ai', 'text');
      return;
    }

    setIsLoading(true);
    addMessage('正在分析招标文件，请稍候...', 'ai', 'text');

    try {
      const result = await aiEnhancedService.analyzeDocument(documentData.content);
      
      if (result.success && result.data) {
        const analysisMessage: Message = {
          id: uuidv4(),
          content: '文档分析完成！以下是提取的关键信息：',
          sender: 'ai',
          timestamp: new Date(),
          type: 'analysis',
          data: result.data
        };
        
        setMessages(prev => [...prev, analysisMessage]);
      } else {
        addMessage(result.error || '文档分析失败', 'ai', 'text');
      }
    } catch (error) {
      addMessage('文档分析过程中出现错误', 'ai', 'text');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateContent = async () => {
    const section = context.includes('技术标') ? 'technicalSolution' : 'pricingStrategy';
    
    setIsLoading(true);
    addMessage(`正在为您生成${context}内容...`, 'ai', 'text');

    try {
      const requirements = documentData?.requirements || [];
      const contextData = { section: context };
      
      const result = context.includes('技术标') 
        ? await aiEnhancedService.generateTechnicalContent(section, requirements, contextData)
        : await aiEnhancedService.generateCommercialContent(section, requirements, contextData);
      
      if (result.success && result.data) {
        const generatedMessage: Message = {
          id: uuidv4(),
          content: result.data,
          sender: 'ai',
          timestamp: new Date(),
          type: 'generation'
        };
        
        setMessages(prev => [...prev, generatedMessage]);
        
        // 回调生成的内容
        if (onContentGenerated) {
          onContentGenerated(result.data);
        }
      } else {
        addMessage(result.error || '内容生成失败', 'ai', 'text');
      }
    } catch (error) {
      addMessage('内容生成过程中出现错误', 'ai', 'text');
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplianceCheck = async () => {
    if (!currentContent) {
      addMessage('请先编写一些内容，我才能进行合规检查。', 'ai', 'text');
      return;
    }

    setIsLoading(true);
    addMessage('正在检查内容合规性...', 'ai', 'text');

    try {
      const requirements = documentData?.requirements || [];
      const bidContent = { content: currentContent, section: context };
      
      const result = await aiEnhancedService.checkCompliance(bidContent, requirements);
      
      if (result.success && result.data) {
        const complianceMessage: Message = {
          id: uuidv4(),
          content: '合规检查完成！',
          sender: 'ai',
          timestamp: new Date(),
          type: 'compliance',
          data: result.data
        };
        
        setMessages(prev => [...prev, complianceMessage]);
      } else {
        addMessage(result.error || '合规检查失败', 'ai', 'text');
      }
    } catch (error) {
      addMessage('合规检查过程中出现错误', 'ai', 'text');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOptimizeContent = async () => {
    if (!currentContent) {
      addMessage('请先编写一些内容，我才能进行优化。', 'ai', 'text');
      return;
    }

    setIsLoading(true);
    addMessage('正在优化内容...', 'ai', 'text');

    try {
      const requirements = documentData?.requirements || [];
      
      const result = await aiEnhancedService.optimizeResponseRelevance(currentContent, requirements);
      
      if (result.success && result.data) {
        const optimizedMessage: Message = {
          id: uuidv4(),
          content: `内容优化完成！以下是优化后的内容：\n\n${result.data}`,
          sender: 'ai',
          timestamp: new Date(),
          type: 'generation'
        };
        
        setMessages(prev => [...prev, optimizedMessage]);
        
        // 回调优化后的内容
        if (onContentGenerated) {
          onContentGenerated(result.data);
        }
      } else {
        addMessage(result.error || '内容优化失败', 'ai', 'text');
      }
    } catch (error) {
      addMessage('内容优化过程中出现错误', 'ai', 'text');
    } finally {
      setIsLoading(false);
    }
  };

  const addMessage = (content: string, sender: 'user' | 'ai', type: string = 'text') => {
    const message: Message = {
      id: uuidv4(),
      content,
      sender,
      timestamp: new Date(),
      type: type as any
    };
    setMessages(prev => [...prev, message]);
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessageContent = (message: Message) => {
    switch (message.type) {
      case 'analysis':
        return (
          <div className="space-y-4">
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown>{message.content}</ReactMarkdown>
            </div>
            {message.data && (
              <div className="bg-blue-50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium text-blue-900">分析结果</h4>
                {message.data.projectInfo && (
                  <div>
                    <h5 className="text-sm font-medium text-blue-800">项目信息</h5>
                    <p className="text-sm text-blue-700">{message.data.projectInfo.name}</p>
                  </div>
                )}
                {message.data.requirements && (
                  <div>
                    <h5 className="text-sm font-medium text-blue-800">关键要求 ({message.data.requirements.length}项)</h5>
                    <div className="text-sm text-blue-700">
                      {message.data.requirements.slice(0, 3).map((req: any, index: number) => (
                        <div key={index} className="truncate">• {req.content}</div>
                      ))}
                      {message.data.requirements.length > 3 && (
                        <div className="text-xs text-blue-600">...还有{message.data.requirements.length - 3}项要求</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        );
        
      case 'compliance':
        return (
          <div className="space-y-4">
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown>{message.content}</ReactMarkdown>
            </div>
            {message.data && (
              <div className="bg-green-50 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-green-900">合规评分</h4>
                  <span className={`text-lg font-bold ${
                    message.data.overallScore >= 80 ? 'text-green-600' : 
                    message.data.overallScore >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {message.data.overallScore}/100
                  </span>
                </div>
                
                {message.data.issues && message.data.issues.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-red-800 flex items-center">
                      <AlertTriangle size={14} className="mr-1" />
                      发现问题 ({message.data.issues.length})
                    </h5>
                    <div className="text-sm text-red-700 space-y-1">
                      {message.data.issues.slice(0, 2).map((issue: any, index: number) => (
                        <div key={index}>• {issue.issue}</div>
                      ))}
                    </div>
                  </div>
                )}
                
                {message.data.suggestions && message.data.suggestions.length > 0 && (
                  <div>
                    <h5 className="text-sm font-medium text-blue-800 flex items-center">
                      <Lightbulb size={14} className="mr-1" />
                      改进建议 ({message.data.suggestions.length})
                    </h5>
                    <div className="text-sm text-blue-700 space-y-1">
                      {message.data.suggestions.slice(0, 2).map((suggestion: any, index: number) => (
                        <div key={index}>• {suggestion.suggestion}</div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        );
        
      default:
        return (
          <div className="prose prose-sm max-w-none">
            <ReactMarkdown>{message.content}</ReactMarkdown>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[700px] flex flex-col"
      >
        {/* 头部 */}
        <div className="border-b px-6 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <Bot size={20} className="text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">AI智能助手</h3>
            <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
              {context}
            </span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={20} />
          </button>
        </div>

        {/* 快捷操作栏 */}
        <div className="border-b px-6 py-3 bg-gray-50">
          <div className="flex flex-wrap gap-2">
            {quickActions.map((action) => (
              <button
                key={action.id}
                onClick={action.action}
                disabled={isLoading}
                className="flex items-center px-3 py-1.5 text-xs bg-white border border-gray-200 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                title={action.description}
              >
                {action.icon}
                <span className="ml-1">{action.label}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[85%] ${
                  message.sender === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-800'
                } rounded-lg px-4 py-3`}>
                  {renderMessageContent(message)}
                  <div className={`text-xs mt-2 ${
                    message.sender === 'user' ? 'text-blue-200' : 'text-gray-500'
                  } text-right`}>
                    {formatTime(message.timestamp)}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg px-4 py-3 flex items-center">
                <Loader2 size={16} className="text-blue-600 animate-spin mr-2" />
                <span className="text-sm text-gray-500">AI正在处理中...</span>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* 输入区域 */}
        <div className="border-t p-4">
          <form onSubmit={handleSubmit} className="flex items-center space-x-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="输入您的问题或需求..."
              className="flex-1 border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            />
            <Button
              type="submit"
              disabled={isLoading || !input.trim()}
            >
              {isLoading ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <Send size={18} />
              )}
            </Button>
          </form>
          <div className="mt-2 text-xs text-gray-500 text-center">
            AI助手支持文档分析、内容生成、合规检查等功能
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EnhancedAiChat;