import React, { useState } from 'react';
import { Lightbulb, Bot } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const ProjectUnderstanding: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">项目理解</h2>
            <p className="text-gray-500 mt-1">展示对项目需求的深入理解</p>
          </div>
          <Button
            onClick={() => setShowAiChat(true)}
          >
            <Bot size={16} className="mr-2" />
            AI辅助编写
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目背景分析
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请分析项目的背景情况，包括项目建设必要性、行业现状等..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              需求分析
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请详细分析项目的具体需求，包括功能需求、性能需求等..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              重点难点分析
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请分析项目实施的重点和难点，以及相应的解决思路..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目目标
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请描述项目的预期目标和成果..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline">保存草稿</Button>
          <Button>确认提交</Button>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="项目理解编写" 
        />
      )}
    </div>
  );
};

export default ProjectUnderstanding;