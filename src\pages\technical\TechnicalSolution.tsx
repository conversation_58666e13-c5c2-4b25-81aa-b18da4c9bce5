import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const TechnicalSolution: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">技术方案</h2>
            <p className="text-gray-500 mt-1">详细的技术实现方案</p>
          </div>
          <Button
            onClick={() => setShowAiChat(true)}
          >
            <Bot size={16} className="mr-2" />
            AI辅助编写
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              总体架构
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请描述系统的总体架构设计，包括技术架构、网络架构等..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              功能设计
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请详细描述各个功能模块的设计方案..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              技术特点
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请说明解决方案的技术特点和创新点..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              性能指标
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请列出系统的主要性能指标和保障措施..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline">保存草稿</Button>
          <Button>确认提交</Button>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="技术方案编写" 
        />
      )}
    </div>
  );
};

export default TechnicalSolution;