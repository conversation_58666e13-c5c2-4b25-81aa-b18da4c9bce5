import express from 'express';
import multer from 'multer';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import axios from 'axios';
import { connectDatabase, checkDatabaseConnection } from './config/database.js';
import File from './models/File.js';
import Project from './models/Project.js';
// Neo4j 集成
import neo4j from 'neo4j-driver';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = process.env.PORT || 3008;

// 连接数据库
let dbConnection = null;
let useDatabase = false;

// Neo4j 连接配置
const NEO4J_URI = process.env.NEO4J_URI || 'bolt://localhost:7687';
const NEO4J_USER = process.env.NEO4J_USER || 'neo4j';
const NEO4J_PASSWORD = process.env.NEO4J_PASSWORD || 'password123';

let neo4jDriver = null;
let useNeo4j = false;

// 初始化数据库连接
(async () => {
  // MongoDB 连接
  dbConnection = await connectDatabase();
  useDatabase = dbConnection !== null;
  console.log(`MongoDB存储模式: ${useDatabase ? 'MongoDB数据库' : 'JSON文件'}`);
  
  // Neo4j 连接
  try {
    neo4jDriver = neo4j.driver(
      NEO4J_URI,
      neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD)
    );
    
    // 测试连接
    const session = neo4jDriver.session();
    await session.run('RETURN 1 as test');
    await session.close();
    
    useNeo4j = true;
    console.log('✅ Neo4j 连接成功');
  } catch (error) {
    console.log('⚠️ Neo4j 连接失败，将跳过图数据库功能:', error.message);
    useNeo4j = false;
  }
})();

// 启用CORS
app.use(cors({
  origin: function(origin, callback) {
    callback(null, true); // 允许所有来源
  },
  credentials: true
}));

// 启用 JSON 解析
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// 服务静态文件
app.use(express.static(path.join(__dirname, 'dist')));

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// 创建上传目录
const uploadDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 创建数据目录存储文件信息
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const filesDataPath = path.join(dataDir, 'files.json');

// 初始化文件数据
let filesData = [];
if (fs.existsSync(filesDataPath)) {
  try {
    const data = fs.readFileSync(filesDataPath, 'utf8');
    const parsedData = JSON.parse(data);
    // 确保解析的数据是数组，如果不是则初始化为空数组
    filesData = Array.isArray(parsedData) ? parsedData : [];
  } catch (error) {
    console.error('读取文件数据失败:', error);
    filesData = [];
  }
}

// 保存文件数据
const saveFilesData = () => {
  try {
    fs.writeFileSync(filesDataPath, JSON.stringify(filesData, null, 2));
  } catch (error) {
    console.error('保存文件数据失败:', error);
  }
};

// 统一的文件名编码处理函数
const normalizeFileName = (originalName) => {
  if (!originalName) return 'unknown_file';
  
  let processedName = originalName;
  
  try {
    // 尝试多种编码检测和转换方式
    const buffer = Buffer.from(originalName, 'binary');
    
    // 检测是否可能是UTF-8编码的文件名
    const utf8Test = buffer.toString('utf8');
    if (utf8Test && utf8Test.length > 0) {
      // 验证UTF-8是否有效
      const reEncodedBuffer = Buffer.from(utf8Test, 'utf8');
      if (reEncodedBuffer.equals(buffer)) {
        processedName = utf8Test;
      }
    }
    
    // 如果上面的方法不行，尝试从latin1转换
    if (processedName === originalName) {
      try {
        const latin1Buffer = Buffer.from(originalName, 'latin1');
        const possibleUtf8 = latin1Buffer.toString('utf8');
        
        // 简单验证：检查是否包含常见中文字符范围
        if (/[\u4e00-\u9fff]/.test(possibleUtf8)) {
          processedName = possibleUtf8;
        }
      } catch (e) {
        console.log('Latin1转换失败:', e.message);
      }
    }
  } catch (error) {
    console.log('文件名编码处理失败:', error.message);
    // 使用原始文件名
    processedName = originalName;
  }
  
  // 清理文件名：移除不安全字符但保留中文
  processedName = processedName
    .replace(/[<>:"/\\|?*\x00-\x1f\x7f]/g, '_') // 移除控制字符和不安全字符
    .replace(/\s+/g, '_') // 多个空格替换为单个下划线
    .replace(/_{2,}/g, '_') // 多个下划线合并为一个
    .trim()
    .substring(0, 200); // 限制长度但比之前更长
  
  return processedName || 'processed_file';
};

// 配置文件存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名，处理中文文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    
    // 使用统一的文件名处理函数
    const normalizedName = normalizeFileName(file.originalname);
    console.log('文件名处理:', file.originalname, '->', normalizedName);
    
    const ext = path.extname(normalizedName);
    const nameWithoutExt = path.basename(normalizedName, ext);
    
    cb(null, uniqueSuffix + '-' + nameWithoutExt + ext);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/zip',
    'application/x-rar-compressed'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型'), false);
  }
};

const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB限制
    files: 10 // 最多10个文件
  }
});

// AI配置
const AI_CONFIG = {
  deepseek: {
    url: 'https://api.deepseek.com/v1',
    apiKey: '***********************************',
    model: 'deepseek-chat'
  }
};

// 健康检查接口
app.get('/api/health', async (req, res) => {
  const dbStatus = checkDatabaseConnection();
  
  // 检查Neo4j连接状态
  let neo4jStatus = { connected: false, status: 'disconnected' };
  if (useNeo4j && neo4jDriver) {
    try {
      const session = neo4jDriver.session();
      await session.run('RETURN 1 as test');
      await session.close();
      neo4jStatus = { connected: true, status: 'connected' };
    } catch (error) {
      neo4jStatus = { connected: false, status: 'connection_error', error: error.message };
    }
  }
  
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version,
    database: {
      mongodb: {
        connected: dbStatus.isConnected,
        status: dbStatus.status,
        type: useDatabase ? 'MongoDB' : 'JSON File'
      },
      neo4j: neo4jStatus
    },
    storage: {
      mode: useDatabase ? 'database' : 'file',
      filesCount: useDatabase ? 'N/A' : filesData.length
    },
    features: {
      knowledgeGraph: useNeo4j
    }
  });
});

// AI聊天接口
app.post('/api/ai/chat', async (req, res) => {
  try {
    console.log('收到AI聊天请求');
    const { message, messages, context } = req.body;
    
    if (!message && !messages) {
      return res.status(400).json({ message: '消息内容不能为空' });
    }
    
    // 构建消息数组
    let chatMessages = [];
    
    // 添加系统消息
    chatMessages.push({
      role: 'system',
      content: `你是一个专业的投标文件制作助手，擅长${context || '标书制作'}相关的内容创作和问题解答。请提供详细、专业的回答。`
    });
    
    // 添加历史消息或单条消息
    if (messages && Array.isArray(messages)) {
      chatMessages.push(...messages);
    } else if (message) {
      chatMessages.push({
        role: 'user',
        content: message
      });
    }
    
    // 调用DeepSeek API
    const response = await axios.post(
      `${AI_CONFIG.deepseek.url}/chat/completions`,
      {
        model: AI_CONFIG.deepseek.model,
        messages: chatMessages,
        temperature: 0.7,
        max_tokens: 2000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.deepseek.apiKey}`
        },
        timeout: 30000
      }
    );
    
    const aiResponse = response.data.choices[0].message.content;
    
    console.log('AI响应成功');
    res.status(200).json({
      message: aiResponse,
      success: true
    });
    
  } catch (error) {
    console.error('AI聊天错误:', error);
    
    let errorMessage = 'AI服务暂时不可用，请稍后再试';
    if (error.response?.data?.error) {
      errorMessage = error.response.data.error.message || errorMessage;
    }
    
    res.status(500).json({ 
      message: errorMessage,
      success: false 
    });
  }
});

// AI内容生成接口
app.post('/api/ai/generate', async (req, res) => {
  try {
    console.log('收到AI内容生成请求');
    const { prompt, context, section } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ message: 'prompt不能为空' });
    }
    
    // 根据不同section构建不同的system message
    const systemMessages = {
      'companyProfile': '你是专业的企业文档撰写专家，擅长撰写公司简介、资质展示等内容。',
      'technicalSolution': '你是技术方案专家，擅长设计技术架构和解决方案。',
      'implementationPlan': '你是项目管理专家，擅长制定详细的实施计划和进度安排。',
      'pricingStrategy': '你是商务报价专家，擅长制定合理的报价策略。',
      'costBreakdown': '你是成本分析专家，擅长进行详细的成本分解和计算。',
      'default': `你是专业的投标文件制作助手，擅长${context || '标书制作'}相关的内容创作。`
    };
    
    const systemMessage = systemMessages[section] || systemMessages.default;
    
    const response = await axios.post(
      `${AI_CONFIG.deepseek.url}/chat/completions`,
      {
        model: AI_CONFIG.deepseek.model,
        messages: [
          {
            role: 'system',
            content: systemMessage
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 3000
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_CONFIG.deepseek.apiKey}`
        },
        timeout: 30000
      }
    );
    
    const generatedContent = response.data.choices[0].message.content;
    
    console.log('AI内容生成成功');
    res.status(200).json({
      content: generatedContent,
      success: true
    });
    
  } catch (error) {
    console.error('AI内容生成错误:', error);
    
    let errorMessage = 'AI内容生成失败，请稍后再试';
    if (error.response?.data?.error) {
      errorMessage = error.response.data.error.message || errorMessage;
    }
    
    res.status(500).json({ 
      message: errorMessage,
      success: false 
    });
  }
});

// 文件上传接口
app.post('/api/files/upload', upload.array('files'), async (req, res) => {
  try {
    console.log('收到文件上传请求');
    console.log('请求体:', req.body);
    console.log('文件信息:', req.files?.map(f => ({ name: f.originalname, size: f.size })));
    
    const files = req.files;
    const category = req.body.category;
    
    if (!files || files.length === 0) {
      console.log('没有文件被上传');
      return res.status(400).json({ message: '没有文件被上传' });
    }
    
    const uploadedFiles = [];
    
    for (const file of files) {
      let fileInfo;
      
      if (useDatabase) {
        // 使用统一的文件名处理函数
        const normalizedName = normalizeFileName(file.originalname);
        console.log('数据库存储 - 原始文件名:', file.originalname, '-> 标准化后:', normalizedName);
        
        // 使用数据库存储
        const fileDoc = new File({
          name: normalizedName,
          originalName: normalizedName,
          filename: file.filename,
          type: path.extname(normalizedName).slice(1).toLowerCase(),
          size: file.size,
          category: category || 'other',
          filePath: file.path,
          description: req.body.description || ''
        });
        
        const savedFile = await fileDoc.save();
        console.log('文件保存到数据库:', savedFile._id);
        
        fileInfo = {
          id: savedFile._id.toString(),
          filename: file.filename,
          originalname: normalizedName,
          size: file.size,
          category: category || '其他'
        };
      } else {
        // 使用JSON文件存储
        const normalizedName = normalizeFileName(file.originalname);
        console.log('JSON存储 - 原始文件名:', file.originalname, '-> 标准化后:', normalizedName);
        
        const jsonFileInfo = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: normalizedName,
          type: path.extname(normalizedName).slice(1).toLowerCase(),
          size: file.size,
          category: category || 'other',
          uploadDate: new Date().toISOString().split('T')[0],
          filePath: file.path,
          filename: file.filename
        };
        
        filesData.push(jsonFileInfo);
        saveFilesData();
        
        fileInfo = {
          id: jsonFileInfo.id,
          filename: file.filename,
          originalname: normalizedName,
          size: file.size,
          category: category || '其他'
        };
      }
      
      uploadedFiles.push(fileInfo);
      
      // 如果启用了Neo4j，创建文件节点
      if (useNeo4j && neo4jDriver) {
        try {
          const session = neo4jDriver.session();
          await session.run(
            `CREATE (f:File {
              id: $id,
              name: $name,
              type: $type,
              size: $size,
              category: $category,
              uploadDate: $uploadDate,
              filePath: $filePath
            })`,
            {
              id: fileInfo.id,
              name: fileInfo.originalname,
              type: path.extname(fileInfo.originalname).slice(1).toLowerCase(),
              size: file.size,
              category: category || 'other',
              uploadDate: new Date().toISOString(),
              filePath: file.path
            }
          );
          await session.close();
          console.log(`文件 ${fileInfo.originalname} 已添加到知识图谱`);
        } catch (error) {
          console.error('添加文件到Neo4j失败:', error.message);
        }
      }
    }
    
    console.log(`成功上传 ${files.length} 个文件`);
    res.status(200).json({
      message: '文件上传成功',
      files: uploadedFiles
    });
  } catch (error) {
    console.error('文件上传错误:', error);
    res.status(500).json({ message: '文件上传失败: ' + error.message });
  }
});

// 分块上传接口
app.post('/api/files/upload-chunk', upload.single('chunk'), (req, res) => {
  try {
    console.log('收到分块上传请求');
    const { chunkIndex, totalChunks, fileName, category } = req.body;
    const chunk = req.file;
    
    if (!chunk) {
      return res.status(400).json({ message: '没有接收到文件块' });
    }
    
    console.log(`处理文件块: ${fileName} - ${parseInt(chunkIndex) + 1}/${totalChunks}`);
    
    // 创建临时目录存储文件块
    const tempDir = path.join(uploadDir, 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    const chunkPath = path.join(tempDir, `${fileName}.part${chunkIndex}`);
    
    // 移动文件块到临时目录
    fs.renameSync(chunk.path, chunkPath);
    
    // 检查是否所有块都已上传
    const allChunksUploaded = Array.from({ length: parseInt(totalChunks) }, (_, i) => {
      return fs.existsSync(path.join(tempDir, `${fileName}.part${i}`));
    }).every(exists => exists);
    
    if (allChunksUploaded) {
      console.log(`所有文件块已上传，开始合并: ${fileName}`);
      
      // 合并文件块
      const finalPath = path.join(uploadDir, `${Date.now()}-${Math.round(Math.random() * 1E9)}-${fileName}`);
      const writeStream = fs.createWriteStream(finalPath);
      
      for (let i = 0; i < parseInt(totalChunks); i++) {
        const chunkPath = path.join(tempDir, `${fileName}.part${i}`);
        const chunkData = fs.readFileSync(chunkPath);
        writeStream.write(chunkData);
        
        // 删除临时文件块
        fs.unlinkSync(chunkPath);
      }
      
      writeStream.end();
      
      // 获取文件信息，处理中文文件名
      const stats = fs.statSync(finalPath);
      const normalizedFileName = normalizeFileName(fileName);
      console.log('分块上传文件名:', fileName, '-> 标准化后:', normalizedFileName);
      
      const fileInfo = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        name: normalizedFileName,
        type: path.extname(normalizedFileName).slice(1).toLowerCase(),
        size: stats.size,
        category: category,
        uploadDate: new Date().toISOString().split('T')[0],
        filePath: finalPath,
        filename: path.basename(finalPath)
      };
      
      // 添加到文件数据中
      filesData.push(fileInfo);
      saveFilesData();
      
      console.log(`文件合并完成: ${fileName}`);
      res.status(200).json({
        message: '文件上传完成',
        file: fileInfo,
        completed: true
      });
    } else {
      // 还有文件块未上传
      res.status(200).json({
        message: `文件块 ${parseInt(chunkIndex) + 1}/${totalChunks} 上传成功`,
        completed: false
      });
    }
  } catch (error) {
    console.error('分块上传错误:', error);
    res.status(500).json({ message: '分块上传失败: ' + error.message });
  }
});

// 获取文件列表接口
app.get('/api/files', async (req, res) => {
  try {
    console.log('收到获取文件列表请求');
    const { category, search, sortBy = 'uploadDate', sortOrder = 'desc', page = 1, limit = 50 } = req.query;
    console.log('查询参数:', { category, search, sortBy, sortOrder });
    
    let filteredFiles = [];
    
    if (useDatabase) {
      // 构建查询条件
      let query = { isActive: true };
      
      if (category && category !== '全部') {
        query.category = category;
      }
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }
      
      // 构建排序条件
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
      
      // 分页查询
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      filteredFiles = await File.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit));
      
      // 转换为前端需要的格式
      filteredFiles = filteredFiles.map(file => ({
        id: file._id.toString(),
        name: file.name,
        type: file.type,
        size: file.size,
        category: file.category,
        uploadDate: file.uploadDate.toISOString().split('T')[0],
        filePath: file.filePath,
        filename: file.filename,
        description: file.description,
        tags: file.tags
      }));
    } else {
      // 使用JSON文件存储
      filteredFiles = filesData;
      
      if (category && category !== '全部') {
        filteredFiles = filteredFiles.filter(file => file.category === category);
      }
      
      if (search) {
        filteredFiles = filteredFiles.filter(file => 
          file.name.toLowerCase().includes(search.toLowerCase()) ||
          (file.description && file.description.toLowerCase().includes(search.toLowerCase()))
        );
      }
      
      // 简单排序
      filteredFiles.sort((a, b) => {
        if (sortOrder === 'desc') {
          return b[sortBy] > a[sortBy] ? 1 : -1;
        } else {
          return a[sortBy] > b[sortBy] ? 1 : -1;
        }
      });
    }
    
    console.log(`返回 ${filteredFiles.length} 个文件`);
    res.status(200).json(filteredFiles);
  } catch (error) {
    console.error('获取文件列表错误:', error);
    res.status(500).json({ message: '获取文件列表失败' });
  }
});

// 删除文件接口
app.delete('/api/files/:fileId', async (req, res) => {
  try {
    console.log('收到删除文件请求');
    const { fileId } = req.params;
    console.log('文件ID:', fileId);
    
    let file;
    
    if (useDatabase) {
      // 使用数据库查找文件
      file = await File.findById(fileId);
      if (!file) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
      
      // 删除物理文件
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
      }
      
      // 软删除
      await file.softDelete();
    } else {
      // 使用JSON文件存储
      const fileIndex = filesData.findIndex(file => file.id === fileId);
      
      if (fileIndex === -1) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
      
      file = filesData[fileIndex];
      
      // 删除物理文件
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
      }
      
      // 从数据中删除
      filesData.splice(fileIndex, 1);
      saveFilesData();
    }
    
    console.log('文件删除成功');
    res.status(200).json({ message: '文件删除成功' });
  } catch (error) {
    console.error('删除文件错误:', error);
    res.status(500).json({ message: '删除文件失败' });
  }
});

// 下载文件接口
app.get('/api/files/:fileId/download', async (req, res) => {
  try {
    console.log('收到下载文件请求');
    const { fileId } = req.params;
    console.log('文件ID:', fileId);
    
    let file;
    
    if (useDatabase) {
      // 使用数据库查找文件
      file = await File.findById(fileId);
      if (!file) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
    } else {
      // 使用JSON文件存储
      file = filesData.find(file => file.id === fileId);
      if (!file) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
    }
    
    if (!fs.existsSync(file.filePath)) {
      console.log('文件已被删除');
      return res.status(404).json({ message: '文件已被删除' });
    }
    
    console.log('开始下载文件:', file.name);
    res.download(file.filePath, file.name);
  } catch (error) {
    console.error('下载文件错误:', error);
    res.status(500).json({ message: '下载文件失败' });
  }
});

// 预览文件接口
app.get('/api/files/:fileId/preview', async (req, res) => {
  try {
    console.log('收到预览文件请求');
    const { fileId } = req.params;
    console.log('文件ID:', fileId);
    
    let file;
    
    if (useDatabase) {
      // 使用数据库查找文件
      file = await File.findById(fileId);
      if (!file) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
    } else {
      // 使用JSON文件存储
      file = filesData.find(file => file.id === fileId);
      if (!file) {
        console.log('文件不存在');
        return res.status(404).json({ message: '文件不存在' });
      }
    }
    
    if (!fs.existsSync(file.filePath)) {
      console.log('文件已被删除');
      return res.status(404).json({ message: '文件已被删除' });
    }
    
    // 根据文件类型设置响应头
    const mimeTypes = {
      'pdf': 'application/pdf',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'txt': 'text/plain'
    };
    
    const mimeType = mimeTypes[file.type] || 'application/octet-stream';
    
    res.setHeader('Content-Type', mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${file.name}"`);
    
    console.log('开始预览文件:', file.name);
    const fileStream = fs.createReadStream(file.filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('预览文件错误:', error);
    res.status(500).json({ message: '预览文件失败' });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ message: '文件大小超过限制(100MB)' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ message: '文件数量超过限制(10个)' });
    }
  }
  
  if (error.message === '不支持的文件类型') {
    return res.status(400).json({ message: '不支持的文件类型' });
  }
  
  console.error('服务器错误:', error);
  res.status(500).json({ message: '服务器内部错误' });
});

// 批量删除文件接口
app.delete('/api/files/batch', async (req, res) => {
  try {
    console.log('收到批量删除文件请求');
    const { fileIds } = req.body;
    
    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return res.status(400).json({ message: '文件ID列表不能为空' });
    }
    
    console.log('删除文件数量:', fileIds.length);
    
    let deletedCount = 0;
    const errors = [];
    
    if (useDatabase) {
      // 使用数据库批量删除
      for (const fileId of fileIds) {
        try {
          const file = await File.findById(fileId);
          if (file) {
            // 删除物理文件
            if (fs.existsSync(file.filePath)) {
              fs.unlinkSync(file.filePath);
            }
            // 软删除
            await file.softDelete();
            deletedCount++;
          }
        } catch (error) {
          errors.push({ fileId, error: error.message });
        }
      }
    } else {
      // 使用JSON文件存储
      for (const fileId of fileIds) {
        const fileIndex = filesData.findIndex(file => file.id === fileId);
        if (fileIndex !== -1) {
          const file = filesData[fileIndex];
          // 删除物理文件
          if (fs.existsSync(file.filePath)) {
            fs.unlinkSync(file.filePath);
          }
          // 从数组中删除
          filesData.splice(fileIndex, 1);
          deletedCount++;
        }
      }
      saveFilesData();
    }
    
    console.log(`成功删除 ${deletedCount} 个文件`);
    res.status(200).json({
      message: `成功删除 ${deletedCount} 个文件`,
      deletedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('批量删除文件错误:', error);
    res.status(500).json({ message: '批量删除文件失败' });
  }
});

// 文件统计接口
app.get('/api/files/stats', async (req, res) => {
  try {
    console.log('收到文件统计请求');
    
    let stats = {
      totalFiles: 0,
      totalSize: 0,
      categoryCounts: {},
      typeCounts: {},
      recentUploads: 0
    };
    
    if (useDatabase) {
      // 使用数据库统计
      const files = await File.find({ isActive: true });
      
      stats.totalFiles = files.length;
      stats.totalSize = files.reduce((sum, file) => sum + file.size, 0);
      
      // 按类别统计
      files.forEach(file => {
        stats.categoryCounts[file.category] = (stats.categoryCounts[file.category] || 0) + 1;
        stats.typeCounts[file.type] = (stats.typeCounts[file.type] || 0) + 1;
      });
      
      // 最近7天上传的文件数量
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      stats.recentUploads = await File.countDocuments({
        isActive: true,
        uploadDate: { $gte: sevenDaysAgo }
      });
    } else {
      // 使用JSON文件存储
      stats.totalFiles = filesData.length;
      stats.totalSize = filesData.reduce((sum, file) => sum + file.size, 0);
      
      filesData.forEach(file => {
        stats.categoryCounts[file.category] = (stats.categoryCounts[file.category] || 0) + 1;
        stats.typeCounts[file.type] = (stats.typeCounts[file.type] || 0) + 1;
      });
      
      // 最近7天上传的文件数量（简化版）
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      stats.recentUploads = filesData.filter(file => 
        new Date(file.uploadDate) >= sevenDaysAgo
      ).length;
    }
    
    console.log('文件统计:', stats);
    res.status(200).json(stats);
  } catch (error) {
    console.error('获取文件统计错误:', error);
    res.status(500).json({ message: '获取文件统计失败' });
  }
});

// 文档导出API
app.post('/api/export/document', async (req, res) => {
  try {
    const { content, title, format, options } = req.body;
    
    console.log('收到文档导出请求:', { title, format });
    
    if (!content || !title || !format) {
      return res.status(400).json({ message: '缺少必要参数' });
    }
    
    // 为不同格式生成导出结果
    let result = {};
    
    switch (format) {
      case 'html':
        result = {
          filename: `${title}.html`,
          content: generateHTMLExport(content, title, options),
          mimeType: 'text/html'
        };
        break;
        
      case 'markdown':
        result = {
          filename: `${title}.md`,
          content: generateMarkdownExport(content, title, options),
          mimeType: 'text/markdown'
        };
        break;
        
      default:
        return res.status(400).json({ message: '不支持的导出格式' });
    }
    
    res.status(200).json({
      message: '文档导出成功',
      result
    });
  } catch (error) {
    console.error('文档导出失败:', error);
    res.status(500).json({ message: '文档导出失败' });
  }
});

// HTML导出函数
const generateHTMLExport = (content, title, options = {}) => {
  const {
    includeStyles = true,
    fontSize = 14,
    margins = { top: 20, bottom: 20, left: 20, right: 20 }
  } = options;
  
  const styles = includeStyles ? `
    <style>
      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        font-size: ${fontSize}px;
        line-height: 1.6;
        color: #333;
        max-width: 800px;
        margin: 0 auto;
        padding: ${margins.top}px ${margins.right}px ${margins.bottom}px ${margins.left}px;
      }
      h1, h2, h3 { color: #2c3e50; margin-top: 2em; margin-bottom: 1em; }
      h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 0.5em; }
      p { margin-bottom: 1em; text-align: justify; }
      table { border-collapse: collapse; width: 100%; margin-bottom: 1em; }
      th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
      th { background-color: #f8f9fa; font-weight: bold; }
      .header { text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 1em; margin-bottom: 2em; }
      @media print { body { margin: 0; padding: 1cm; } }
    </style>
  ` : '';
  
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    ${styles}
</head>
<body>
    <div class="header">
        <h1>${title}</h1>
        <p>生成日期: ${new Date().toLocaleDateString('zh-CN')}</p>
    </div>
    <div class="content">${content}</div>
</body>
</html>`;
};

// Markdown导出函数
const generateMarkdownExport = (content, title) => {
  let markdown = `# ${title}\n\n`;
  markdown += `*生成日期: ${new Date().toLocaleDateString('zh-CN')}*\n\n`;
  
  // 基本的HTML到Markdown转换
  let convertedContent = content
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
    .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
    .replace(/<[^>]+>/g, '');
  
  return markdown + convertedContent;
};

// 知识图谱API端点

// 获取知识图谱统计信息
app.get('/api/knowledge-graph/stats', async (req, res) => {
  try {
    if (!useNeo4j || !neo4jDriver) {
      return res.status(503).json({ message: '知识图谱服务不可用' });
    }

    const session = neo4jDriver.session();
    
    try {
      // 获取节点统计
      const nodeStats = await session.run(`
        MATCH (n) 
        RETURN labels(n)[0] as label, count(n) as count
        ORDER BY count DESC
      `);
      
      // 获取关系统计
      const relationStats = await session.run(`
        MATCH ()-[r]-() 
        RETURN type(r) as type, count(r) as count
        ORDER BY count DESC
      `);
      
      const stats = {
        nodes: nodeStats.records.map(record => ({
          label: record.get('label'),
          count: record.get('count').toNumber()
        })),
        relationships: relationStats.records.map(record => ({
          type: record.get('type'),
          count: record.get('count').toNumber()
        })),
        totalNodes: nodeStats.records.reduce((sum, record) => sum + record.get('count').toNumber(), 0),
        totalRelationships: relationStats.records.reduce((sum, record) => sum + record.get('count').toNumber(), 0)
      };
      
      res.status(200).json(stats);
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('获取知识图谱统计失败:', error);
    res.status(500).json({ message: '获取统计信息失败' });
  }
});

// 搜索知识图谱
app.post('/api/knowledge-graph/search', async (req, res) => {
  try {
    if (!useNeo4j || !neo4jDriver) {
      return res.status(503).json({ message: '知识图谱服务不可用' });
    }

    const { query, type, limit = 20 } = req.body;
    
    if (!query) {
      return res.status(400).json({ message: '搜索查询不能为空' });
    }

    const session = neo4jDriver.session();
    
    try {
      let cypherQuery;
      
      // 根据类型构建查询
      if (type === 'files') {
        cypherQuery = `
          MATCH (f:File) 
          WHERE toLower(f.name) CONTAINS toLower($query) 
             OR toLower(f.category) CONTAINS toLower($query)
          RETURN f
          LIMIT $limit
        `;
      } else if (type === 'projects') {
        cypherQuery = `
          MATCH (p:Project) 
          WHERE toLower(p.name) CONTAINS toLower($query) 
             OR toLower(p.description) CONTAINS toLower($query)
          RETURN p
          LIMIT $limit
        `;
      } else {
        // 全局搜索
        cypherQuery = `
          MATCH (n) 
          WHERE any(prop in keys(n) WHERE toLower(toString(n[prop])) CONTAINS toLower($query))
          RETURN n, labels(n) as labels
          LIMIT $limit
        `;
      }
      
      const result = await session.run(cypherQuery, { query, limit: parseInt(limit) });
      
      const nodes = result.records.map(record => {
        const node = type ? record.get(type.slice(0, 1)) : record.get('n');
        return {
          id: node.properties.id,
          properties: node.properties,
          labels: type ? [type.slice(0, -1).charAt(0).toUpperCase() + type.slice(1, -1)] : record.get('labels')
        };
      });
      
      res.status(200).json({ nodes, total: nodes.length });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('知识图谱搜索失败:', error);
    res.status(500).json({ message: '搜索失败' });
  }
});

// 获取节点关系
app.get('/api/knowledge-graph/relations/:nodeId', async (req, res) => {
  try {
    if (!useNeo4j || !neo4jDriver) {
      return res.status(503).json({ message: '知识图谱服务不可用' });
    }

    const { nodeId } = req.params;
    const { depth = 1 } = req.query;
    
    const session = neo4jDriver.session();
    
    try {
      const result = await session.run(`
        MATCH path = (n {id: $nodeId})-[r*1..${parseInt(depth)}]-(related)
        RETURN n, relationships(path) as rels, related
        LIMIT 50
      `, { nodeId });
      
      const relations = result.records.map(record => ({
        source: record.get('n').properties,
        relationships: record.get('rels').map(rel => ({
          type: rel.type,
          properties: rel.properties
        })),
        target: record.get('related').properties
      }));
      
      res.status(200).json({ relations });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('获取节点关系失败:', error);
    res.status(500).json({ message: '获取关系失败' });
  }
});

// 分析文档并创建知识图谱
app.post('/api/knowledge-graph/analyze-document', async (req, res) => {
  try {
    if (!useNeo4j || !neo4jDriver) {
      return res.status(503).json({ message: '知识图谱服务不可用' });
    }

    const { fileId, content, extractedInfo } = req.body;
    
    if (!fileId || !content) {
      return res.status(400).json({ message: '文件ID和内容不能为空' });
    }

    const session = neo4jDriver.session();
    
    try {
      // 创建或更新文档节点
      await session.run(`
        MERGE (d:Document {id: $fileId})
        SET d.content = $content, 
            d.analyzed = true, 
            d.analyzedAt = datetime(),
            d.extractedInfo = $extractedInfo
      `, { fileId, content, extractedInfo: JSON.stringify(extractedInfo || {}) });

      // 如果有提取的信息，创建相关节点
      if (extractedInfo) {
        // 创建需求节点
        if (extractedInfo.requirements) {
          for (let i = 0; i < extractedInfo.requirements.length; i++) {
            const req = extractedInfo.requirements[i];
            await session.run(`
              MERGE (r:Requirement {id: $reqId})
              SET r.content = $content, r.type = $type, r.priority = $priority
              WITH r
              MATCH (d:Document {id: $fileId})
              MERGE (d)-[:HAS_REQUIREMENT]->(r)
            `, {
              reqId: `${fileId}_req_${i}`,
              content: req.content || req,
              type: req.type || 'general',
              priority: req.priority || 'medium',
              fileId
            });
          }
        }

        // 创建关键词节点
        if (extractedInfo.keywords) {
          for (const keyword of extractedInfo.keywords) {
            await session.run(`
              MERGE (k:Keyword {name: $keyword})
              WITH k
              MATCH (d:Document {id: $fileId})
              MERGE (d)-[:CONTAINS_KEYWORD]->(k)
            `, { keyword, fileId });
          }
        }
      }
      
      res.status(200).json({ message: '文档分析完成，知识图谱已更新' });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('文档分析失败:', error);
    res.status(500).json({ message: '文档分析失败' });
  }
});

// 获取图谱可视化数据
app.get('/api/knowledge-graph/visualization', async (req, res) => {
  try {
    if (!useNeo4j || !neo4jDriver) {
      return res.status(503).json({ message: '知识图谱服务不可用' });
    }

    const { limit = 100 } = req.query;
    const session = neo4jDriver.session();
    
    try {
      const result = await session.run(`
        MATCH (n)-[r]->(m)
        RETURN n, r, m
        LIMIT $limit
      `, { limit: parseInt(limit) });
      
      const nodes = new Map();
      const links = [];
      
      result.records.forEach(record => {
        const source = record.get('n');
        const relationship = record.get('r');
        const target = record.get('m');
        
        // 添加源节点
        if (!nodes.has(source.properties.id)) {
          nodes.set(source.properties.id, {
            id: source.properties.id,
            label: source.properties.name || source.properties.id,
            group: source.labels[0] || 'Unknown',
            properties: source.properties
          });
        }
        
        // 添加目标节点
        if (!nodes.has(target.properties.id)) {
          nodes.set(target.properties.id, {
            id: target.properties.id,
            label: target.properties.name || target.properties.id,
            group: target.labels[0] || 'Unknown',
            properties: target.properties
          });
        }
        
        // 添加关系
        links.push({
          source: source.properties.id,
          target: target.properties.id,
          type: relationship.type,
          properties: relationship.properties
        });
      });
      
      res.status(200).json({
        nodes: Array.from(nodes.values()),
        links: links
      });
    } finally {
      await session.close();
    }
  } catch (error) {
    console.error('获取可视化数据失败:', error);
    res.status(500).json({ message: '获取可视化数据失败' });
  }
});

// SPA路由处理 - 对于非API请求返回index.html
app.get('*', (req, res) => {
  // 如果是API请求，返回404
  if (req.path.startsWith('/api/')) {
    console.log(`404 - 未找到API路径: ${req.method} ${req.originalUrl}`);
    return res.status(404).json({ message: '接口不存在' });
  }
  
  // 对于其他请求，返回前端应用
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// API 404处理
app.use('/api/*', (req, res) => {
  console.log(`404 - 未找到API路径: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: '接口不存在' });
});

app.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
  console.log(`上传目录: ${uploadDir}`);
  console.log(`数据目录: ${dataDir}`);
  console.log(`API健康检查: http://localhost:${port}/api/health`);
});