import React, { useState, useEffect } from 'react';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// 最简化的文件管理应用
function MinimalApp() {
  const [files, setFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [category, setCategory] = useState('other');

  // 获取文件列表
  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/files');
      if (response.ok) {
        const data = await response.json();
        setFiles(Array.isArray(data) ? data : []);
        console.log('获取文件成功:', data.length, '个文件');
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.error('获取文件失败:', error);
      toast.error('获取文件列表失败');
      setFiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 上传文件
  const uploadFiles = async () => {
    if (selectedFiles.length === 0) {
      toast.error('请选择文件');
      return;
    }

    try {
      const formData = new FormData();
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });
      formData.append('category', category);

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`成功上传 ${selectedFiles.length} 个文件`);
        setSelectedFiles([]);
        setShowUpload(false);
        fetchFiles(); // 刷新文件列表
      } else {
        const error = await response.json();
        throw new Error(error.message || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      toast.error(`上传失败: ${error.message}`);
    }
  };

  // 删除文件
  const deleteFile = async (fileId: string, fileName: string) => {
    if (!confirm(`确定删除文件 "${fileName}"?`)) return;

    try {
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('文件删除成功');
        fetchFiles();
      } else {
        throw new Error('删除失败');
      }
    } catch (error) {
      console.error('删除文件失败:', error);
      toast.error('删除文件失败');
    }
  };

  // 下载文件
  const downloadFile = (fileId: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = `/api/files/${fileId}/download`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  useEffect(() => {
    fetchFiles();
  }, []);

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', padding: '20px' }}>
      <ToastContainer position="top-right" autoClose={3000} />
      
      {/* 标题栏 */}
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px', 
        marginBottom: '20px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>
            AI投标文件制作平台 - 文件管理
          </h1>
          <button
            onClick={() => setShowUpload(true)}
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            上传文件
          </button>
        </div>
      </div>

      {/* 文件统计 */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ margin: '0 0 10px 0', fontSize: '18px' }}>文件统计</h2>
        <p>总文件数: {files.length} 个</p>
      </div>

      {/* 文件列表 */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h2 style={{ margin: 0, fontSize: '18px' }}>文件列表</h2>
          <button
            onClick={fetchFiles}
            disabled={isLoading}
            style={{
              backgroundColor: '#6b7280',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {isLoading ? '刷新中...' : '刷新'}
          </button>
        </div>

        {isLoading ? (
          <p>加载中...</p>
        ) : files.length === 0 ? (
          <p>暂无文件</p>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ borderBottom: '2px solid #e5e7eb' }}>
                  <th style={{ padding: '12px', textAlign: 'left' }}>文件名</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>类型</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>大小</th>
                  <th style={{ padding: '12px', textAlign: 'left' }}>上传日期</th>
                  <th style={{ padding: '12px', textAlign: 'right' }}>操作</th>
                </tr>
              </thead>
              <tbody>
                {files.map((file, index) => (
                  <tr key={file.id || index} style={{ borderBottom: '1px solid #e5e7eb' }}>
                    <td style={{ padding: '12px' }}>{file.name}</td>
                    <td style={{ padding: '12px' }}>{file.type}</td>
                    <td style={{ padding: '12px' }}>{formatFileSize(file.size)}</td>
                    <td style={{ padding: '12px' }}>{file.uploadDate}</td>
                    <td style={{ padding: '12px', textAlign: 'right' }}>
                      <button
                        onClick={() => downloadFile(file.id, file.name)}
                        style={{
                          backgroundColor: '#10b981',
                          color: 'white',
                          border: 'none',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          marginRight: '8px'
                        }}
                      >
                        下载
                      </button>
                      <button
                        onClick={() => deleteFile(file.id, file.name)}
                        style={{
                          backgroundColor: '#ef4444',
                          color: 'white',
                          border: 'none',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          cursor: 'pointer'
                        }}
                      >
                        删除
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 上传对话框 */}
      {showUpload && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '30px',
            borderRadius: '8px',
            width: '500px',
            maxWidth: '90vw'
          }}>
            <h3 style={{ margin: '0 0 20px 0' }}>上传文件</h3>
            
            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '8px' }}>文件类别:</label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px'
                }}
              >
                <option value="bidding">招标文件</option>
                <option value="certificates">资质证书</option>
                <option value="cases">案例材料</option>
                <option value="technical">技术文档</option>
                <option value="other">其他</option>
              </select>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '8px' }}>选择文件:</label>
              <input
                type="file"
                multiple
                onChange={(e) => setSelectedFiles(Array.from(e.target.files || []))}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #d1d5db',
                  borderRadius: '4px'
                }}
              />
            </div>

            {selectedFiles.length > 0 && (
              <div style={{ marginBottom: '20px' }}>
                <p>已选择 {selectedFiles.length} 个文件:</p>
                <ul style={{ margin: '10px 0', paddingLeft: '20px' }}>
                  {selectedFiles.map((file, index) => (
                    <li key={index}>{file.name} ({formatFileSize(file.size)})</li>
                  ))}
                </ul>
              </div>
            )}

            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
              <button
                onClick={() => {
                  setShowUpload(false);
                  setSelectedFiles([]);
                }}
                style={{
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                取消
              </button>
              <button
                onClick={uploadFiles}
                disabled={selectedFiles.length === 0}
                style={{
                  backgroundColor: selectedFiles.length > 0 ? '#3b82f6' : '#9ca3af',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '4px',
                  cursor: selectedFiles.length > 0 ? 'pointer' : 'not-allowed'
                }}
              >
                上传
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default MinimalApp;