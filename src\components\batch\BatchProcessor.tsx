import React, { useState, useRef } from 'react';
import { Upload, FileText, Play, Pause, CheckCircle, AlertTriangle, Download, Trash2 } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { toast } from 'react-toastify';
import { aiEnhancedService } from '../../services/aiEnhanced';

interface BatchFile {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  result?: any;
  error?: string;
}

interface BatchProcessorProps {
  onComplete?: (results: any[]) => void;
}

const BatchProcessor: React.FC<BatchProcessorProps> = ({ onComplete }) => {
  const [files, setFiles] = useState<BatchFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files).map(file => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        file,
        status: 'pending' as const,
        progress: 0
      }));
      
      setFiles(prev => [...prev, ...newFiles]);
      toast.success(`已添加 ${newFiles.length} 个文件到批处理队列`);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files) {
      const newFiles = Array.from(e.dataTransfer.files).map(file => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        file,
        status: 'pending' as const,
        progress: 0
      }));
      
      setFiles(prev => [...prev, ...newFiles]);
      toast.success(`已添加 ${newFiles.length} 个文件到批处理队列`);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  };

  const processFile = async (batchFile: BatchFile): Promise<any> => {
    return new Promise(async (resolve, reject) => {
      try {
        // 更新状态为处理中
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id 
            ? { ...f, status: 'processing', progress: 10 }
            : f
        ));

        // 读取文件内容
        const content = await readFileContent(batchFile.file);
        
        // 更新进度
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id 
            ? { ...f, progress: 30 }
            : f
        ));

        // 调用AI分析
        const analysisResult = await aiEnhancedService.analyzeDocument(content);
        
        // 更新进度
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id 
            ? { ...f, progress: 70 }
            : f
        ));

        if (analysisResult.success) {
          // 生成技术标内容
          const technicalContent = await aiEnhancedService.generateTechnicalContent(
            'companyProfile',
            analysisResult.data?.requirements || [],
            { fileName: batchFile.file.name }
          );

          // 更新进度
          setFiles(prev => prev.map(f => 
            f.id === batchFile.id 
              ? { ...f, progress: 90 }
              : f
          ));

          const result = {
            fileName: batchFile.file.name,
            analysis: analysisResult.data,
            technicalContent: technicalContent.data,
            processedAt: new Date().toISOString()
          };

          // 完成处理
          setFiles(prev => prev.map(f => 
            f.id === batchFile.id 
              ? { ...f, status: 'completed', progress: 100, result }
              : f
          ));

          resolve(result);
        } else {
          throw new Error(analysisResult.error || '分析失败');
        }
      } catch (error) {
        // 处理错误
        const errorMessage = error instanceof Error ? error.message : '处理失败';
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id 
            ? { ...f, status: 'error', error: errorMessage }
            : f
        ));
        reject(error);
      }
    });
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsText(file);
    });
  };

  const startBatchProcessing = async () => {
    if (files.length === 0) {
      toast.error('请先添加要处理的文件');
      return;
    }

    setIsProcessing(true);
    setIsPaused(false);
    setCurrentIndex(0);

    const results: any[] = [];

    for (let i = 0; i < files.length; i++) {
      if (isPaused) {
        break;
      }

      setCurrentIndex(i);
      const file = files[i];

      if (file.status === 'pending') {
        try {
          const result = await processFile(file);
          results.push(result);
          
          // 添加延迟避免API限制
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          console.error(`处理文件 ${file.file.name} 失败:`, error);
        }
      }
    }

    setIsProcessing(false);
    
    if (results.length > 0) {
      toast.success(`批处理完成，成功处理 ${results.length} 个文件`);
      if (onComplete) {
        onComplete(results);
      }
    }
  };

  const pauseProcessing = () => {
    setIsPaused(true);
    setIsProcessing(false);
    toast.info('批处理已暂停');
  };

  const resumeProcessing = () => {
    setIsPaused(false);
    startBatchProcessing();
  };

  const clearAll = () => {
    setFiles([]);
    setCurrentIndex(0);
    toast.success('已清空所有文件');
  };

  const downloadResults = () => {
    const completedFiles = files.filter(f => f.status === 'completed' && f.result);
    
    if (completedFiles.length === 0) {
      toast.error('没有可下载的结果');
      return;
    }

    const results = completedFiles.map(f => f.result);
    const dataStr = JSON.stringify(results, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `batch_processing_results_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast.success('结果已下载');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'error':
        return <AlertTriangle size={16} className="text-red-500" />;
      case 'processing':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <FileText size={16} className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const completedCount = files.filter(f => f.status === 'completed').length;
  const errorCount = files.filter(f => f.status === 'error').length;
  const totalCount = files.length;

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">批量处理</h2>
            <p className="text-gray-500">批量分析和处理招标文件</p>
          </div>
          
          <div className="flex items-center gap-3">
            {files.length > 0 && (
              <>
                {!isProcessing && !isPaused && (
                  <Button onClick={startBatchProcessing}>
                    <Play size={16} className="mr-2" />
                    开始处理
                  </Button>
                )}
                
                {isProcessing && (
                  <Button variant="outline" onClick={pauseProcessing}>
                    <Pause size={16} className="mr-2" />
                    暂停
                  </Button>
                )}
                
                {isPaused && (
                  <Button onClick={resumeProcessing}>
                    <Play size={16} className="mr-2" />
                    继续
                  </Button>
                )}
                
                {completedCount > 0 && (
                  <Button variant="outline" onClick={downloadResults}>
                    <Download size={16} className="mr-2" />
                    下载结果
                  </Button>
                )}
                
                <Button variant="outline" onClick={clearAll}>
                  <Trash2 size={16} className="mr-2" />
                  清空
                </Button>
              </>
            )}
          </div>
        </div>

        {/* 统计信息 */}
        {totalCount > 0 && (
          <div className="grid grid-cols-4 gap-4 mb-6">
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-gray-800">{totalCount}</div>
              <div className="text-sm text-gray-500">总文件数</div>
            </div>
            <div className="bg-blue-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-blue-600">{currentIndex + 1}</div>
              <div className="text-sm text-blue-600">当前处理</div>
            </div>
            <div className="bg-green-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-green-600">{completedCount}</div>
              <div className="text-sm text-green-600">已完成</div>
            </div>
            <div className="bg-red-50 rounded-lg p-3 text-center">
              <div className="text-2xl font-bold text-red-600">{errorCount}</div>
              <div className="text-sm text-red-600">失败</div>
            </div>
          </div>
        )}

        {/* 文件上传区域 */}
        <div
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">添加文件到批处理队列</h3>
          <p className="text-gray-500 mb-4">
            拖拽文件到此处，或点击选择文件
          </p>
          <p className="text-sm text-gray-400">
            支持 PDF、DOC、DOCX、TXT 等格式
          </p>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">处理队列</h3>
            <div className="space-y-3">
              {files.map((batchFile, index) => (
                <div
                  key={batchFile.id}
                  className={`flex items-center justify-between p-4 border rounded-lg ${
                    index === currentIndex && isProcessing ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center flex-1 min-w-0">
                    {getStatusIcon(batchFile.status)}
                    <div className="ml-3 flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {batchFile.file.name}
                      </p>
                      <div className="flex items-center mt-1">
                        <span className={`px-2 py-0.5 text-xs rounded-full ${getStatusColor(batchFile.status)}`}>
                          {batchFile.status === 'pending' && '等待中'}
                          {batchFile.status === 'processing' && '处理中'}
                          {batchFile.status === 'completed' && '已完成'}
                          {batchFile.status === 'error' && '失败'}
                        </span>
                        {batchFile.status === 'processing' && (
                          <span className="ml-2 text-xs text-gray-500">
                            {batchFile.progress}%
                          </span>
                        )}
                        {batchFile.error && (
                          <span className="ml-2 text-xs text-red-600">
                            {batchFile.error}
                          </span>
                        )}
                      </div>
                      
                      {/* 进度条 */}
                      {batchFile.status === 'processing' && (
                        <div className="mt-2 w-full bg-gray-200 rounded-full h-1">
                          <div
                            className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                            style={{ width: `${batchFile.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                      {(batchFile.file.size / 1024).toFixed(1)} KB
                    </span>
                    {batchFile.status === 'pending' && (
                      <button
                        onClick={() => removeFile(batchFile.id)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <Trash2 size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default BatchProcessor;