import axios from 'axios';

// 创建API实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加请求日志
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`API响应: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API响应错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);

// 文件API
export const fileApi = {
  // 上传文件
  uploadFiles: async (files: File[], category: string, onProgress?: (progress: number) => void) => {
    try {
      console.log(`准备上传 ${files.length} 个文件，类别: ${category}`);
      
      const formData = new FormData();
      
      // 添加文件到FormData
      files.forEach((file, index) => {
        formData.append('files', file);
        console.log(`添加文件 ${index + 1}: ${file.name} (${file.size} bytes)`);
      });
      
      // 添加分类信息
      formData.append('category', category);
      
      const response = await api.post('/files/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 300000, // 5分钟超时
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(`上传进度: ${percentCompleted}%`);
            if (onProgress) {
              onProgress(percentCompleted);
            }
          }
        }
      });
      
      console.log('文件上传成功:', response.data);
      return response;
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  },
  
  // 分块上传文件
  uploadFileChunk: async (chunk: Blob, chunkIndex: number, totalChunks: number, fileName: string, category: string) => {
    try {
      const formData = new FormData();
      formData.append('chunk', chunk);
      formData.append('chunkIndex', chunkIndex.toString());
      formData.append('totalChunks', totalChunks.toString());
      formData.append('fileName', fileName);
      formData.append('category', category);
      
      const response = await api.post('/files/upload-chunk', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 120000, // 2分钟超时
      });
      
      return response;
    } catch (error) {
      console.error('分块上传失败:', error);
      throw error;
    }
  },
  
  // 获取文件列表
  getFiles: async (category?: string) => {
    try {
      console.log(`获取文件列表，类别: ${category || '全部'}`);
      const params = category && category !== '全部' ? { category } : {};
      const response = await api.get('/files', { params });
      console.log(`获取到 ${response.data?.length || 0} 个文件`);
      return response;
    } catch (error) {
      console.error('获取文件列表失败:', error);
      throw error;
    }
  },
  
  // 删除文件
  deleteFile: async (fileId: string) => {
    try {
      console.log(`删除文件: ${fileId}`);
      const response = await api.delete(`/files/${fileId}`);
      console.log('文件删除成功');
      return response;
    } catch (error) {
      console.error('删除文件失败:', error);
      throw error;
    }
  },
  
  // 下载文件
  downloadFile: async (fileId: string, fileName: string) => {
    try {
      console.log(`下载文件: ${fileId} - ${fileName}`);
      const response = await api.get(`/files/${fileId}/download`, {
        responseType: 'blob'
      });
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      console.log('文件下载完成');
      return response;
    } catch (error) {
      console.error('下载文件失败:', error);
      throw error;
    }
  },
  
  // 预览文件
  previewFile: async (fileId: string) => {
    try {
      console.log(`预览文件: ${fileId}`);
      const response = await api.get(`/files/${fileId}/preview`);
      return response;
    } catch (error) {
      console.error('预览文件失败:', error);
      throw error;
    }
  }
};

// 健康检查API
export const healthApi = {
  // 检查服务器状态
  checkHealth: async () => {
    try {
      const response = await api.get('/health');
      return response;
    } catch (error) {
      console.error('健康检查失败:', error);
      throw error;
    }
  }
};

export default api;