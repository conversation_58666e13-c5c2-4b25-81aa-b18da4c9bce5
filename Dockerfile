# 多阶段构建
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm install

# 复制源代码
COPY . .

# 构建前端应用
RUN npm run build

# 生产阶段
FROM node:18-alpine AS production

# 安装dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 只安装生产依赖
RUN npm install --only=production && npm cache clean --force

# 从builder阶段复制构建产物
COPY --from=builder /app/dist ./dist

# 复制服务器文件和其他必要文件
COPY server.js ./
COPY config/ ./config/
COPY models/ ./models/

# 创建必要的目录
RUN mkdir -p uploads data logs

# 设置目录权限
RUN chown -R nodejs:nodejs /app

# 切换到应用用户
USER nodejs

# 暴露端口
EXPOSE 3008

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3008/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 使用dumb-init启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "server.js"]