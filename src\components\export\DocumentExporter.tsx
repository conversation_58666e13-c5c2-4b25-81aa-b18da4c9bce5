import React, { useState } from 'react';
import { Download, FileText, File, Globe, Settings } from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { toast } from 'react-toastify';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Document, Packer, Paragraph, TextRun, HeadingLevel } from 'docx';
import { saveAs } from 'file-saver';

interface ExportOptions {
  format: 'pdf' | 'docx' | 'html';
  includeImages: boolean;
  pageSize: 'A4' | 'A3' | 'Letter';
  orientation: 'portrait' | 'landscape';
  fontSize: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  watermark?: string;
  headerFooter: {
    includeHeader: boolean;
    includeFooter: boolean;
    headerText: string;
    footerText: string;
  };
}

interface DocumentExporterProps {
  content: string;
  title: string;
  onExport?: (format: string) => void;
}

const DocumentExporter: React.FC<DocumentExporterProps> = ({
  content,
  title,
  onExport
}) => {
  const [showOptions, setShowOptions] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    includeImages: true,
    pageSize: 'A4',
    orientation: 'portrait',
    fontSize: 12,
    margins: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20
    },
    headerFooter: {
      includeHeader: true,
      includeFooter: true,
      headerText: title,
      footerText: `第 {pageNumber} 页 共 {totalPages} 页`
    }
  });

  const exportToPDF = async () => {
    try {
      setIsExporting(true);
      
      // 创建临时容器
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '210mm'; // A4 width
      tempDiv.style.padding = '20mm';
      tempDiv.style.fontFamily = 'Arial, sans-serif';
      tempDiv.style.fontSize = `${exportOptions.fontSize}px`;
      tempDiv.style.lineHeight = '1.6';
      tempDiv.style.color = '#000';
      tempDiv.style.backgroundColor = '#fff';
      
      document.body.appendChild(tempDiv);
      
      // 转换为canvas
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });
      
      // 创建PDF
      const pdf = new jsPDF({
        orientation: exportOptions.orientation,
        unit: 'mm',
        format: exportOptions.pageSize.toLowerCase()
      });
      
      const imgData = canvas.toDataURL('image/png');
      const imgWidth = exportOptions.orientation === 'portrait' ? 210 : 297;
      const pageHeight = exportOptions.orientation === 'portrait' ? 297 : 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      
      let position = 0;
      
      // 添加页眉
      if (exportOptions.headerFooter.includeHeader) {
        pdf.setFontSize(10);
        pdf.text(exportOptions.headerFooter.headerText, 20, 15);
      }
      
      // 添加内容
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
      
      // 处理多页
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        
        // 添加页眉
        if (exportOptions.headerFooter.includeHeader) {
          pdf.setFontSize(10);
          pdf.text(exportOptions.headerFooter.headerText, 20, 15);
        }
        
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      
      // 添加页脚
      if (exportOptions.headerFooter.includeFooter) {
        const pageCount = pdf.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
          pdf.setPage(i);
          pdf.setFontSize(10);
          const footerText = exportOptions.headerFooter.footerText
            .replace('{pageNumber}', i.toString())
            .replace('{totalPages}', pageCount.toString());
          pdf.text(footerText, 20, pageHeight - 10);
        }
      }
      
      // 保存文件
      pdf.save(`${title}.pdf`);
      
      // 清理
      document.body.removeChild(tempDiv);
      
      toast.success('PDF导出成功');
    } catch (error) {
      console.error('PDF导出失败:', error);
      toast.error('PDF导出失败');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToDocx = async () => {
    try {
      setIsExporting(true);
      
      // 解析HTML内容为文档结构
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      
      const paragraphs: any[] = [];
      
      // 递归处理HTML元素
      const processElement = (element: Element) => {
        if (element.tagName === 'H1' || element.tagName === 'H2' || element.tagName === 'H3') {
          paragraphs.push(
            new Paragraph({
              text: element.textContent || '',
              heading: element.tagName === 'H1' ? HeadingLevel.HEADING_1 : 
                      element.tagName === 'H2' ? HeadingLevel.HEADING_2 : 
                      HeadingLevel.HEADING_3,
            })
          );
        } else if (element.tagName === 'P') {
          const runs: TextRun[] = [];
          
          // 处理段落中的格式
          element.childNodes.forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
              runs.push(new TextRun(node.textContent || ''));
            } else if (node.nodeType === Node.ELEMENT_NODE) {
              const el = node as Element;
              const text = el.textContent || '';
              
              runs.push(new TextRun({
                text,
                bold: el.tagName === 'STRONG' || el.tagName === 'B',
                italics: el.tagName === 'EM' || el.tagName === 'I',
                underline: el.tagName === 'U' ? {} : undefined,
              }));
            }
          });
          
          paragraphs.push(new Paragraph({ children: runs }));
        } else if (element.tagName === 'UL' || element.tagName === 'OL') {
          // 处理列表
          Array.from(element.children).forEach(li => {
            paragraphs.push(
              new Paragraph({
                text: `• ${li.textContent}`,
                bullet: { level: 0 }
              })
            );
          });
        } else {
          // 递归处理子元素
          Array.from(element.children).forEach(processElement);
        }
      };
      
      Array.from(tempDiv.children).forEach(processElement);
      
      // 创建文档
      const doc = new Document({
        sections: [{
          properties: {},
          headers: exportOptions.headerFooter.includeHeader ? {
            default: new Paragraph({
              text: exportOptions.headerFooter.headerText,
              alignment: 'center'
            })
          } : undefined,
          footers: exportOptions.headerFooter.includeFooter ? {
            default: new Paragraph({
              text: exportOptions.headerFooter.footerText,
              alignment: 'center'
            })
          } : undefined,
          children: paragraphs
        }]
      });
      
      // 生成并保存文件
      const blob = await Packer.toBlob(doc);
      saveAs(blob, `${title}.docx`);
      
      toast.success('Word文档导出成功');
    } catch (error) {
      console.error('Word导出失败:', error);
      toast.error('Word文档导出失败');
    } finally {
      setIsExporting(false);
    }
  };

  const exportToHTML = async () => {
    try {
      setIsExporting(true);
      
      const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: ${exportOptions.fontSize}px;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: ${exportOptions.margins.top}px ${exportOptions.margins.right}px ${exportOptions.margins.bottom}px ${exportOptions.margins.left}px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 2em;
            margin-bottom: 1em;
        }
        h1 { font-size: 2em; }
        h2 { font-size: 1.5em; }
        h3 { font-size: 1.3em; }
        p { margin-bottom: 1em; }
        ul, ol { margin-bottom: 1em; padding-left: 2em; }
        li { margin-bottom: 0.5em; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 1em; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 1em 0;
            padding-left: 1em;
            color: #666;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f4f4f4;
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 1em;
            margin-bottom: 2em;
        }
        .footer {
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 1em;
            margin-top: 2em;
            color: #666;
            font-size: 0.9em;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    ${exportOptions.headerFooter.includeHeader ? `<div class="header"><h1>${exportOptions.headerFooter.headerText}</h1></div>` : ''}
    
    <div class="content">
        ${content}
    </div>
    
    ${exportOptions.headerFooter.includeFooter ? `<div class="footer">${exportOptions.headerFooter.footerText}</div>` : ''}
    
    <script>
        // 添加打印功能
        function printDocument() {
            window.print();
        }
        
        // 添加导出为PDF功能（需要浏览器支持）
        function exportToPDF() {
            window.print();
        }
    </script>
</body>
</html>`;
      
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      saveAs(blob, `${title}.html`);
      
      toast.success('HTML文档导出成功');
    } catch (error) {
      console.error('HTML导出失败:', error);
      toast.error('HTML文档导出失败');
    } finally {
      setIsExporting(false);
    }
  };

  const handleExport = async () => {
    if (onExport) {
      onExport(exportOptions.format);
    }

    switch (exportOptions.format) {
      case 'pdf':
        await exportToPDF();
        break;
      case 'docx':
        await exportToDocx();
        break;
      case 'html':
        await exportToHTML();
        break;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-800">文档导出</h3>
            <p className="text-sm text-gray-500">将文档导出为不同格式</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowOptions(!showOptions)}
            >
              <Settings size={16} className="mr-1" />
              设置
            </Button>
            
            <Button
              onClick={handleExport}
              disabled={isExporting || !content}
            >
              {isExporting ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Download size={16} className="mr-2" />
              )}
              {isExporting ? '导出中...' : '导出'}
            </Button>
          </div>
        </div>

        {/* 快速导出按钮 */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <button
            onClick={() => setExportOptions(prev => ({ ...prev, format: 'pdf' }))}
            className={`p-3 border rounded-lg text-center transition-colors ${
              exportOptions.format === 'pdf'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <FileText className="mx-auto mb-2" size={24} />
            <div className="text-sm font-medium">PDF</div>
            <div className="text-xs text-gray-500">便携文档格式</div>
          </button>
          
          <button
            onClick={() => setExportOptions(prev => ({ ...prev, format: 'docx' }))}
            className={`p-3 border rounded-lg text-center transition-colors ${
              exportOptions.format === 'docx'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <File className="mx-auto mb-2" size={24} />
            <div className="text-sm font-medium">Word</div>
            <div className="text-xs text-gray-500">Microsoft Word</div>
          </button>
          
          <button
            onClick={() => setExportOptions(prev => ({ ...prev, format: 'html' }))}
            className={`p-3 border rounded-lg text-center transition-colors ${
              exportOptions.format === 'html'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <Globe className="mx-auto mb-2" size={24} />
            <div className="text-sm font-medium">HTML</div>
            <div className="text-xs text-gray-500">网页格式</div>
          </button>
        </div>

        {/* 导出选项 */}
        {showOptions && (
          <div className="border-t pt-4 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  页面大小
                </label>
                <select
                  value={exportOptions.pageSize}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    pageSize: e.target.value as 'A4' | 'A3' | 'Letter' 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="A4">A4</option>
                  <option value="A3">A3</option>
                  <option value="Letter">Letter</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  页面方向
                </label>
                <select
                  value={exportOptions.orientation}
                  onChange={(e) => setExportOptions(prev => ({ 
                    ...prev, 
                    orientation: e.target.value as 'portrait' | 'landscape' 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="portrait">纵向</option>
                  <option value="landscape">横向</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                字体大小: {exportOptions.fontSize}px
              </label>
              <input
                type="range"
                min="10"
                max="18"
                value={exportOptions.fontSize}
                onChange={(e) => setExportOptions(prev => ({ 
                  ...prev, 
                  fontSize: parseInt(e.target.value) 
                }))}
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.headerFooter.includeHeader}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    headerFooter: { ...prev.headerFooter, includeHeader: e.target.checked }
                  }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">包含页眉</span>
              </label>
              
              {exportOptions.headerFooter.includeHeader && (
                <input
                  type="text"
                  value={exportOptions.headerFooter.headerText}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    headerFooter: { ...prev.headerFooter, headerText: e.target.value }
                  }))}
                  placeholder="页眉文本"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              )}
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={exportOptions.headerFooter.includeFooter}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    headerFooter: { ...prev.headerFooter, includeFooter: e.target.checked }
                  }))}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">包含页脚</span>
              </label>
              
              {exportOptions.headerFooter.includeFooter && (
                <input
                  type="text"
                  value={exportOptions.headerFooter.footerText}
                  onChange={(e) => setExportOptions(prev => ({
                    ...prev,
                    headerFooter: { ...prev.headerFooter, footerText: e.target.value }
                  }))}
                  placeholder="页脚文本 (支持 {pageNumber} 和 {totalPages})"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              )}
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default DocumentExporter;