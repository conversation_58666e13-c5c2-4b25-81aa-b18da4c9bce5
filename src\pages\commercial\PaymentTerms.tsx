import React, { useState } from 'react';
import { Bot, Save, Plus, Trash2 } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

interface PaymentStage {
  id: string;
  stage: string;
  percentage: number;
  amount: number;
  condition: string;
  description: string;
}

const PaymentTerms: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);
  const [totalAmount, setTotalAmount] = useState(1000000);
  const [paymentStages, setPaymentStages] = useState<PaymentStage[]>([
    {
      id: '1',
      stage: '预付款',
      percentage: 30,
      amount: 300000,
      condition: '合同签订后',
      description: '合同签订后5个工作日内支付'
    },
    {
      id: '2',
      stage: '进度款',
      percentage: 40,
      amount: 400000,
      condition: '系统上线',
      description: '系统测试通过并正式上线后支付'
    },
    {
      id: '3',
      stage: '尾款',
      percentage: 30,
      amount: 300000,
      condition: '验收通过',
      description: '最终验收通过后支付'
    }
  ]);

  const addPaymentStage = () => {
    const newStage: PaymentStage = {
      id: Date.now().toString(),
      stage: '',
      percentage: 0,
      amount: 0,
      condition: '',
      description: ''
    };
    setPaymentStages([...paymentStages, newStage]);
  };

  const removePaymentStage = (id: string) => {
    setPaymentStages(paymentStages.filter(stage => stage.id !== id));
  };

  const updatePaymentStage = (id: string, field: keyof PaymentStage, value: string | number) => {
    setPaymentStages(paymentStages.map(stage => {
      if (stage.id === id) {
        const updatedStage = { ...stage, [field]: value };
        if (field === 'percentage') {
          updatedStage.amount = totalAmount * (Number(value) / 100);
        }
        return updatedStage;
      }
      return stage;
    }));
  };

  const totalPercentage = paymentStages.reduce((sum, stage) => sum + stage.percentage, 0);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">付款条件</h2>
            <p className="text-gray-500">设置项目付款方式和条件</p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAiChat(true)}
            >
              <Bot size={16} className="mr-2" />
              AI辅助
            </Button>
            <Button>
              <Save size={16} className="mr-2" />
              保存
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目总金额（元）
            </label>
            <input
              type="number"
              className="w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={totalAmount}
              onChange={(e) => setTotalAmount(parseFloat(e.target.value) || 0)}
            />
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款阶段</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款比例(%)</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款金额(元)</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">付款条件</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">说明</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paymentStages.map((stage) => (
                  <tr key={stage.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={stage.stage}
                        onChange={(e) => updatePaymentStage(stage.id, 'stage', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        className="w-24 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={stage.percentage}
                        onChange={(e) => updatePaymentStage(stage.id, 'percentage', parseFloat(e.target.value) || 0)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stage.amount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={stage.condition}
                        onChange={(e) => updatePaymentStage(stage.id, 'condition', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={stage.description}
                        onChange={(e) => updatePaymentStage(stage.id, 'description', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => removePaymentStage(stage.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="bg-gray-50">
                  <td className="px-6 py-4 text-right font-medium">总计：</td>
                  <td className="px-6 py-4 text-gray-900 font-medium">
                    {totalPercentage}%
                    {totalPercentage !== 100 && (
                      <span className="text-red-500 text-xs ml-2">
                        (总比例需等于100%)
                      </span>
                    )}
                  </td>
                  <td className="px-6 py-4 text-gray-900 font-medium">{totalAmount.toLocaleString()}元</td>
                  <td colSpan={3}></td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={addPaymentStage}
            >
              <Plus size={16} className="mr-2" />
              添加付款阶段
            </Button>
          </div>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="付款条件制定" 
        />
      )}
    </div>
  );
};

export default PaymentTerms;