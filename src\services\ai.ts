import OpenAI from 'openai';
import axios from 'axios';

// AI提供商配置
interface AiProviderConfig {
  url: string;
  apiKey: string;
  isDefault: boolean;
}

// AI配置
const aiProviders: Record<string, AiProviderConfig> = {
  deepseek: {
    url: 'https://api.deepseek.com/v1',
    apiKey: '***********************************',
    isDefault: true
  },
  alibaba: {
    url: 'https://bailian.aliyuncs.com/v1/qwen/generation',
    apiKey: 'sk-28ec47f5eff6404aa98329a9f56ada05',
    isDefault: false
  },
  tencent: {
    url: 'https://ai.tencentcloudapi.com',
    apiKey: 'sk-tsfTXZUNgkePyo5SBWBaLI0ayxA6USvnkoVxZTSgGrQpxaJ6',
    isDefault: false
  }
};

// 获取默认AI提供商
const getDefaultProvider = (): AiProviderConfig => {
  const defaultProvider = Object.values(aiProviders).find(provider => provider.isDefault);
  if (!defaultProvider) {
    // 如果没有设置默认值，使用第一个
    return Object.values(aiProviders)[0];
  }
  return defaultProvider;
};

// 创建OpenAI客户端
const createOpenAIClient = (provider: AiProviderConfig) => {
  return new OpenAI({
    apiKey: provider.apiKey,
    baseURL: provider.url,
  });
};

// 分析文档
export const analyzeDocument = async (documentContent: string) => {
  try {
    const provider = getDefaultProvider();
    const openai = createOpenAIClient(provider);
    
    const response = await openai.chat.completions.create({
      model: 'deepseek-chat', // 根据提供商不同，这里会有变化
      messages: [
        {
          role: 'system',
          content: '你是一个专业的标书分析助手，擅长分析招标文件并提取关键信息。'
        },
        {
          role: 'user',
          content: `请分析以下招标文件内容，提取关键要求、评分标准、技术规格和商务要求：\n\n${documentContent}`
        }
      ],
      temperature: 0.7,
    });
    
    return {
      success: true,
      data: response.choices[0].message.content
    };
  } catch (error) {
    console.error('AI文档分析错误:', error);
    return {
      success: false,
      error: '文档分析失败，请稍后重试'
    };
  }
};

// 生成内容
export const generateContent = async (prompt: string, context: string) => {
  try {
    const provider = getDefaultProvider();
    const openai = createOpenAIClient(provider);
    
    const response = await openai.chat.completions.create({
      model: 'deepseek-chat', // 根据提供商不同，这里会有变化
      messages: [
        {
          role: 'system',
          content: `你是一个专业的标书制作助手，擅长生成高质量的${context}内容。`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
    });
    
    return {
      success: true,
      data: response.choices[0].message.content
    };
  } catch (error) {
    console.error('AI内容生成错误:', error);
    return {
      success: false,
      error: '内容生成失败，请稍后重试'
    };
  }
};

// 聊天
export const chatCompletion = async (messages: Array<{role: string, content: string}>, context: string) => {
  try {
    const provider = getDefaultProvider();
    const openai = createOpenAIClient(provider);
    
    // 添加系统消息
    const systemMessage = {
      role: 'system',
      content: `你是一个专业的标书制作助手，擅长${context}相关的内容创作和问题解答。请提供详细、专业的回答。`
    };
    
    const allMessages = [systemMessage, ...messages];
    
    const response = await openai.chat.completions.create({
      model: 'deepseek-chat', // 根据提供商不同，这里会有变化
      messages: allMessages as any,
      temperature: 0.7,
    });
    
    return {
      success: true,
      data: response.choices[0].message.content
    };
  } catch (error) {
    console.error('AI聊天错误:', error);
    return {
      success: false,
      error: '聊天请求失败，请稍后重试'
    };
  }
};

// 文生图
export const generateImage = async (prompt: string) => {
  try {
    // 使用腾讯云AI进行文生图
    const provider = aiProviders.tencent;
    
    // 此处为模拟，实际项目中需要根据腾讯云API的具体要求实现
    const response = await axios.post(
      `${provider.url}/image/generations`,
      {
        prompt: prompt,
        n: 1,
        size: '1024x1024'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${provider.apiKey}`
        }
      }
    );
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('AI图像生成错误:', error);
    return {
      success: false,
      error: '图像生成失败，请稍后重试'
    };
  }
};

export default {
  analyzeDocument,
  generateContent,
  chatCompletion,
  generateImage,
};