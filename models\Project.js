import mongoose from 'mongoose';

// 项目模型
const projectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  client: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    default: ''
  },
  category: {
    type: String,
    required: true,
    enum: ['政府采购', '国有企业', '民营企业', '国际项目']
  },
  budget: {
    type: String,
    default: ''
  },
  bidDeadline: {
    type: Date,
    required: true
  },
  projectStart: Date,
  projectEnd: Date,
  status: {
    type: String,
    enum: ['准备中', '制作中', '已提交', '中标', '未中标', '已完成'],
    default: '准备中'
  },
  
  // 招标文件信息
  bidDocument: {
    fileId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'File'
    },
    analysisResult: {
      projectInfo: mongoose.Schema.Types.Mixed,
      requirements: [mongoose.Schema.Types.Mixed],
      scoringCriteria: [mongoose.Schema.Types.Mixed],
      technicalSpecs: [mongoose.Schema.Types.Mixed],
      commercialTerms: [mongoose.Schema.Types.Mixed],
      timeline: mongoose.Schema.Types.Mixed,
      qualifications: [mongoose.Schema.Types.Mixed]
    },
    lastAnalyzed: Date
  },
  
  // 技术标内容
  technicalBid: {
    companyProfile: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    projectUnderstanding: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    technicalSolution: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    implementationPlan: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    qualityAssurance: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    }
  },
  
  // 商务标内容
  commercialBid: {
    pricingStrategy: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    costBreakdown: {
      content: String,
      totalPrice: Number,
      breakdown: [mongoose.Schema.Types.Mixed],
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    paymentTerms: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    },
    contractConditions: {
      content: String,
      lastUpdated: Date,
      completionStatus: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
      }
    }
  },
  
  // 关联文件
  attachments: [{
    fileId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'File'
    },
    category: String,
    description: String
  }],
  
  // 合规检查记录
  complianceChecks: [{
    section: String,
    checkDate: {
      type: Date,
      default: Date.now
    },
    score: Number,
    issues: [mongoose.Schema.Types.Mixed],
    suggestions: [mongoose.Schema.Types.Mixed]
  }],
  
  // 项目团队
  team: [{
    name: String,
    role: String,
    contact: String
  }],
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// 索引
projectSchema.index({ status: 1, bidDeadline: 1 });
projectSchema.index({ name: 'text', description: 'text' });
projectSchema.index({ client: 1, category: 1 });

// 虚拟字段 - 整体完成度
projectSchema.virtual('overallCompletion').get(function() {
  const technical = this.technicalBid;
  const commercial = this.commercialBid;
  
  const techCompletion = (
    (technical.companyProfile?.completionStatus || 0) +
    (technical.projectUnderstanding?.completionStatus || 0) +
    (technical.technicalSolution?.completionStatus || 0) +
    (technical.implementationPlan?.completionStatus || 0) +
    (technical.qualityAssurance?.completionStatus || 0)
  ) / 5;
  
  const commCompletion = (
    (commercial.pricingStrategy?.completionStatus || 0) +
    (commercial.costBreakdown?.completionStatus || 0) +
    (commercial.paymentTerms?.completionStatus || 0) +
    (commercial.contractConditions?.completionStatus || 0)
  ) / 4;
  
  return Math.round((techCompletion + commCompletion) / 2);
});

// 实例方法 - 更新完成状态
projectSchema.methods.updateSectionCompletion = function(bidType, section, completion) {
  if (this[bidType] && this[bidType][section]) {
    this[bidType][section].completionStatus = completion;
    this[bidType][section].lastUpdated = new Date();
    return this.save();
  }
  return Promise.reject(new Error('Invalid section'));
};

// 静态方法 - 查找即将到期的项目
projectSchema.statics.findUpcoming = function(days = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    bidDeadline: { $lte: futureDate, $gte: new Date() },
    status: { $in: ['准备中', '制作中'] },
    isActive: true
  }).sort({ bidDeadline: 1 });
};

export default mongoose.model('Project', projectSchema);