import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Bo<PERSON> } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const ImplementationPlan: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">实施计划</h2>
            <p className="text-gray-500 mt-1">项目实施的具体计划和安排</p>
          </div>
          <Button
            onClick={() => setShowAiChat(true)}
          >
            <Bot size={16} className="mr-2" />
            AI辅助编写
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目团队
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请描述项目团队的组织结构和人员配置..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              实施方案
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请详细说明项目实施的具体方案和步骤..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              进度计划
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请列出项目实施的详细进度计划..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              风险管理
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请说明项目实施过程中的风险管理措施..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline">保存草稿</Button>
          <Button>确认提交</Button>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="实施计划编写" 
        />
      )}
    </div>
  );
};

export default ImplementationPlan;