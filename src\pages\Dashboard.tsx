import React from 'react';
import { PlusCircle, FileText, DollarSign, CheckCircle, Clock, FileUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import UploadModal from '../components/files/UploadModal';

const Dashboard: React.FC = () => {
  const [showUploadModal, setShowUploadModal] = React.useState(false);

  const statCards = [
    { title: '进行中项目', value: '2', icon: <Clock size={20} className="text-orange-500" />, color: 'bg-orange-100' },
    { title: '已完成项目', value: '5', icon: <CheckCircle size={20} className="text-green-500" />, color: 'bg-green-100' },
    { title: '技术标文档', value: '7', icon: <FileText size={20} className="text-blue-500" />, color: 'bg-blue-100' },
    { title: '商务标文档', value: '7', icon: <DollarSign size={20} className="text-purple-500" />, color: 'bg-purple-100' },
  ];

  return (
    <div className="space-y-6">
      {/* 欢迎区域 */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl p-8 text-white shadow-lg"
      >
        <h1 className="text-3xl font-bold mb-2">欢迎使用标书制作平台</h1>
        <p className="mb-6 opacity-90">高效智能的标书解决方案，助您轻松应对各类投标</p>
        <div className="flex flex-wrap gap-4">
          <Button 
            variant="white" 
            size="lg"
            onClick={() => setShowUploadModal(true)}
          >
            <FileUp size={18} className="mr-2" />
            上传招标文件
          </Button>
          <Link to="/technical">
            <Button variant="outline-white" size="lg">
              <PlusCircle size={18} className="mr-2" />
              开始制作标书
            </Button>
          </Link>
        </div>
      </motion.div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Card className="h-full">
              <div className="flex items-center">
                <div className={`p-3 rounded-full mr-4 ${card.color}`}>
                  {card.icon}
                </div>
                <div>
                  <h3 className="text-gray-500 text-sm font-medium">{card.title}</h3>
                  <p className="text-2xl font-bold text-gray-800">{card.value}</p>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* 最近项目 */}
      <Card className="p-0">
        <div className="p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">最近项目</h2>
        </div>
        <div className="p-6">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">招标单位</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投标截止日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">城市智能交通系统建设项目</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">某市交通委员会</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-03-15</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-700">进行中</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <Link to="/technical" className="text-blue-600 hover:text-blue-800">查看</Link>
                  </td>
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">企业财务管理系统升级项目</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">某大型企业集团</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-02-28</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-700">已完成</span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <Link to="/technical" className="text-blue-600 hover:text-blue-800">查看</Link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </Card>

      {/* 上传模态框 */}
      {showUploadModal && (
        <UploadModal onClose={() => setShowUploadModal(false)} />
      )}
    </div>
  );
};

export default Dashboard;