import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { fileApi, healthApi } from '../services/api';

const FileManagementTest: React.FC = () => {
  const [files, setFiles] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [serverStatus, setServerStatus] = useState<'unknown' | 'online' | 'offline'>('unknown');

  const fetchFiles = async () => {
    try {
      setIsLoading(true);
      console.log('开始获取文件列表...');
      
      const response = await fileApi.getFiles();
      console.log('文件API响应:', response);
      
      if (response.status === 200) {
        setFiles(Array.isArray(response.data) ? response.data : []);
        setServerStatus('online');
        console.log('文件列表加载成功:', response.data);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      setServerStatus('offline');
      toast.error('获取文件列表失败，请检查服务器连接');
      setFiles([]);
    } finally {
      setIsLoading(false);
    }
  };

  const checkServerStatus = async () => {
    try {
      console.log('检查服务器状态...');
      await healthApi.checkHealth();
      setServerStatus('online');
      console.log('服务器在线');
    } catch (error) {
      setServerStatus('offline');
      console.error('服务器离线:', error);
    }
  };

  useEffect(() => {
    console.log('FileManagementTest组件已挂载');
    checkServerStatus();
    fetchFiles();
  }, []);

  if (isLoading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">文件管理测试页面</h1>
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">文件管理测试页面</h1>
      
      <div className="mb-4">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${
            serverStatus === 'online' ? 'bg-green-500' : 
            serverStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
          }`}></div>
          <span className="text-sm">
            服务器状态: {serverStatus === 'online' ? '在线' : serverStatus === 'offline' ? '离线' : '检查中...'}
          </span>
        </div>
      </div>

      <div className="mb-4">
        <p>文件总数: {files.length}</p>
      </div>

      {files.length > 0 ? (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">文件列表:</h2>
          {files.slice(0, 5).map((file, index) => (
            <div key={file.id || index} className="p-3 border rounded">
              <p className="font-medium">{file.name}</p>
              <p className="text-sm text-gray-600">类型: {file.type} | 大小: {file.size} bytes</p>
            </div>
          ))}
          {files.length > 5 && (
            <p className="text-sm text-gray-500">...还有 {files.length - 5} 个文件</p>
          )}
        </div>
      ) : (
        <p>暂无文件</p>
      )}
    </div>
  );
};

export default FileManagementTest;