import React, { useState, useRef, useEffect } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Save, Bot, Eye, RotateCcw, Download, Share2, Users } from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import EnhancedAiChat from '../ai/EnhancedAiChat';

interface RichTextEditorProps {
  title: string;
  content: string;
  onChange: (content: string) => void;
  documentData?: any;
  requirements?: any[];
  onSave?: () => void;
  onExport?: (format: 'pdf' | 'docx' | 'html') => void;
  collaborators?: any[];
  isCollaborative?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  title,
  content,
  onChange,
  documentData,
  requirements = [],
  onSave,
  onExport,
  collaborators = [],
  isCollaborative = false
}) => {
  const [showAiChat, setShowAiChat] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const [savedContent, setSavedContent] = useState(content);
  const [showExportMenu, setShowExportMenu] = useState(false);
  const [showCollaborators, setShowCollaborators] = useState(false);
  const quillRef = useRef<ReactQuill>(null);

  // 富文本编辑器配置
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'font': [] }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'align': [] }],
      ['link', 'image', 'video'],
      ['blockquote', 'code-block'],
      ['clean']
    ],
    history: {
      delay: 1000,
      maxStack: 50,
      userOnly: false
    }
  };

  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike',
    'color', 'background',
    'script',
    'list', 'bullet', 'indent',
    'direction', 'align',
    'link', 'image', 'video',
    'blockquote', 'code-block'
  ];

  useEffect(() => {
    // 自动保存功能
    const autoSaveTimer = setInterval(() => {
      if (content !== savedContent) {
        handleSave();
      }
    }, 30000); // 30秒自动保存

    return () => clearInterval(autoSaveTimer);
  }, [content, savedContent]);

  const handleSave = () => {
    setSavedContent(content);
    if (onSave) {
      onSave();
    }
  };

  const handleRevert = () => {
    onChange(savedContent);
  };

  const handleAiGenerated = (generatedContent: string) => {
    onChange(generatedContent);
  };

  const handleExport = (format: 'pdf' | 'docx' | 'html') => {
    if (onExport) {
      onExport(format);
    }
    setShowExportMenu(false);
  };

  const hasUnsavedChanges = content !== savedContent;

  return (
    <div className="space-y-4">
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div>
              <h3 className="text-lg font-medium text-gray-800">{title}</h3>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>{content.length} 字符</span>
                {hasUnsavedChanges && (
                  <span className="text-orange-500">• 未保存</span>
                )}
                {isCollaborative && (
                  <span className="text-blue-500">• 协作模式</span>
                )}
              </div>
            </div>
            
            {/* 协作者头像 */}
            {isCollaborative && collaborators.length > 0 && (
              <div className="flex items-center space-x-1">
                {collaborators.slice(0, 3).map((collaborator, index) => (
                  <div
                    key={index}
                    className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs"
                    title={collaborator.name}
                  >
                    {collaborator.name.charAt(0)}
                  </div>
                ))}
                {collaborators.length > 3 && (
                  <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white text-xs">
                    +{collaborators.length - 3}
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPreview(!isPreview)}
            >
              <Eye size={16} className="mr-1" />
              {isPreview ? '编辑' : '预览'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAiChat(true)}
            >
              <Bot size={16} className="mr-1" />
              AI助手
            </Button>

            {isCollaborative && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCollaborators(!showCollaborators)}
              >
                <Users size={16} className="mr-1" />
                协作
              </Button>
            )}

            <div className="relative">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowExportMenu(!showExportMenu)}
              >
                <Download size={16} className="mr-1" />
                导出
              </Button>
              
              {showExportMenu && (
                <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <button
                    onClick={() => handleExport('pdf')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100"
                  >
                    导出 PDF
                  </button>
                  <button
                    onClick={() => handleExport('docx')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100"
                  >
                    导出 Word
                  </button>
                  <button
                    onClick={() => handleExport('html')}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100"
                  >
                    导出 HTML
                  </button>
                </div>
              )}
            </div>
            
            {hasUnsavedChanges && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRevert}
              >
                <RotateCcw size={16} className="mr-1" />
                撤销
              </Button>
            )}
            
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              <Save size={16} className="mr-1" />
              保存
            </Button>
          </div>
        </div>

        {isPreview ? (
          <div className="min-h-[400px] p-4 border border-gray-200 rounded-md bg-white">
            <div 
              className="prose max-w-none"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          </div>
        ) : (
          <div className="border border-gray-200 rounded-md">
            <ReactQuill
              ref={quillRef}
              theme="snow"
              value={content}
              onChange={onChange}
              modules={modules}
              formats={formats}
              style={{ minHeight: '400px' }}
              placeholder={`请输入${title}内容...`}
            />
          </div>
        )}

        {/* 协作者面板 */}
        {showCollaborators && isCollaborative && (
          <div className="mt-4 p-4 bg-gray-50 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 mb-3">当前协作者</h4>
            <div className="space-y-2">
              {collaborators.map((collaborator, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                      {collaborator.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-700">{collaborator.name}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      collaborator.status === 'online' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {collaborator.status === 'online' ? '在线' : '离线'}
                    </span>
                  </div>
                  <span className="text-xs text-gray-500">{collaborator.role}</span>
                </div>
              ))}
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-200">
              <Button size="sm" variant="outline" fullWidth>
                <Share2 size={14} className="mr-1" />
                邀请协作者
              </Button>
            </div>
          </div>
        )}

        {/* 要求响应提示 */}
        {requirements.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 mb-2">需要响应的要求：</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {requirements.slice(0, 3).map((req, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span className="truncate">{req.content}</span>
                </li>
              ))}
              {requirements.length > 3 && (
                <li className="text-blue-600">
                  ...还有 {requirements.length - 3} 项要求
                </li>
              )}
            </ul>
          </div>
        )}
      </Card>

      {showAiChat && (
        <EnhancedAiChat
          onClose={() => setShowAiChat(false)}
          context={title}
          documentData={documentData}
          currentContent={content}
          onContentGenerated={handleAiGenerated}
        />
      )}
    </div>
  );
};

export default RichTextEditor;