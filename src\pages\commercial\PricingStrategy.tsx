import React, { useState } from 'react';
import { Bot, Save } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const PricingStrategy: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">报价策略</h2>
            <p className="text-gray-500">制定合理的报价策略，提高中标概率</p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAiChat(true)}
            >
              <Bot size={16} className="mr-2" />
              AI辅助
            </Button>
            <Button>
              <Save size={16} className="mr-2" />
              保存
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              总体报价策略
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="描述项目的总体报价策略..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              竞争分析
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="分析竞争对手可能的报价策略..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              价格优势
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="说明本项目的价格优势..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              风险控制
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="描述价格风险控制措施..."
            />
          </div>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="报价策略制定" 
        />
      )}
    </div>
  );
};

export default PricingStrategy;