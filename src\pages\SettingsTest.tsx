import React, { useState } from 'react';
import { Settings, Server, Database, Bot } from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const SettingsTest: React.FC = () => {
  const [activeTab, setActiveTab] = useState('system');

  const tabs = [
    { id: 'system', label: '系统设置', icon: <Settings size={18} /> },
    { id: 'api', label: 'API设置', icon: <Server size={18} /> },
    { id: 'database', label: '数据库设置', icon: <Database size={18} /> },
    { id: 'ai', label: 'AI设置', icon: <Bot size={18} /> },
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <Card>
        <h1 className="text-2xl font-bold mb-6">设置页面测试</h1>
        
        <div className="flex flex-col md:flex-row gap-6">
          {/* 左侧导航 */}
          <div className="w-full md:w-64">
            <div className="space-y-2">
              {tabs.map(tab => (
                <button
                  key={tab.id}
                  type="button"
                  className={`w-full flex items-center px-4 py-2 text-left rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <span className="mr-3">{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
          
          {/* 右侧内容 */}
          <div className="flex-1">
            {activeTab === 'system' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">系统设置</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      系统名称
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="AI投标文件制作平台"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      文件存储路径
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="/app/uploads"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      单文件大小限制
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                      <option value="50">50 MB</option>
                      <option value="100">100 MB</option>
                      <option value="200">200 MB</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'api' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">API设置</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      DeepSeek API Key
                    </label>
                    <input
                      type="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      placeholder="输入API密钥"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API URL
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="https://api.deepseek.com/v1"
                    />
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'database' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">数据库设置</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      MongoDB连接字符串
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="mongodb://localhost:27020/ai-toubiao"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Neo4j连接字符串
                    </label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="neo4j://localhost:7690"
                    />
                  </div>
                </div>
              </div>
            )}
            
            {activeTab === 'ai' && (
              <div>
                <h2 className="text-xl font-semibold mb-4">AI设置</h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      默认AI模型
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md">
                      <option value="deepseek">DeepSeek</option>
                      <option value="qwen">通义千问</option>
                      <option value="chatgpt">ChatGPT</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      最大Token数
                    </label>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      defaultValue="4000"
                    />
                  </div>
                </div>
              </div>
            )}
            
            <div className="mt-6 flex justify-end space-x-3">
              <Button variant="outline">取消</Button>
              <Button>保存设置</Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SettingsTest;
