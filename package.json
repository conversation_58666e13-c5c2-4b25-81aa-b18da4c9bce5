{"type": "module", "dependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "axios": "^1.6.2", "cors": "^2.8.5", "docx": "^8.5.0", "express": "^4.21.2", "file-saver": "^2.0.5", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.303.0", "mongoose": "^8.17.0", "multer": "^1.4.5-lts.1", "neo4j-driver": "^5.28.1", "openai": "^5.11.0", "quill": "^1.3.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-router-dom": "^6.20.1", "react-toastify": "^9.1.3", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.1.0", "uuid": "^9.0.1", "vite": "^4.4.5"}, "devDependencies": {"@types/file-saver": "^2.0.7", "autoprefixer": "^10.4.17", "tailwindcss": "^3.4.1"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node server.js"}}