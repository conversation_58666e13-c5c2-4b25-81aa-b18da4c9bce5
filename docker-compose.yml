services:
  # 前端+后端应用
  app:
    build: .
    ports:
      - "3010:3008"
      - "5175:5174"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=*********************************************************************
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password123
      - POSTGRES_URI=***********************************************/ai_toubiao
    volumes:
      - ./uploads:/app/uploads
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - mongodb
      - neo4j
      - postgres
    networks:
      - ai-toubiao-network
    restart: unless-stopped

  # MongoDB数据库
  mongodb:
    image: mongo:6.0
    container_name: ai-toubiao-mongodb
    ports:
      - "27020:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=ai-toubiao
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js:ro
    networks:
      - ai-toubiao-network
    restart: unless-stopped

  # Neo4j图数据库
  neo4j:
    image: neo4j:5.15-community
    container_name: ai-toubiao-neo4j
    ports:
      - "7480:7474"  # HTTP
      - "7690:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password123
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - ai-toubiao-network
    restart: unless-stopped

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai-toubiao-postgres
    ports:
      - "5435:5432"
    environment:
      - POSTGRES_DB=ai_toubiao
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - ai-toubiao-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai-toubiao-redis
    ports:
      - "6385:6379"
    command: redis-server --appendonly yes --requirepass password123
    volumes:
      - redis_data:/data
    networks:
      - ai-toubiao-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: ai-toubiao-nginx
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - ai-toubiao-network
    restart: unless-stopped

networks:
  ai-toubiao-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local