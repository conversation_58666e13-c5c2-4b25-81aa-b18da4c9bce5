// MongoDB初始化脚本
db = db.getSiblingDB('ai-toubiao');

// 创建用户
db.createUser({
  user: 'aiuser',
  pwd: 'password123',
  roles: [
    {
      role: 'readWrite',
      db: 'ai-toubiao'
    }
  ]
});

// 创建集合和索引
db.createCollection('files');
db.createCollection('projects');
db.createCollection('templates');
db.createCollection('bids');
db.createCollection('users');

// 为files集合创建索引
db.files.createIndex({ "name": "text", "description": "text" });
db.files.createIndex({ "category": 1 });
db.files.createIndex({ "uploadDate": -1 });
db.files.createIndex({ "isActive": 1 });

// 为projects集合创建索引
db.projects.createIndex({ "name": "text", "description": "text" });
db.projects.createIndex({ "status": 1 });
db.projects.createIndex({ "createdAt": -1 });

// 为templates集合创建索引
db.templates.createIndex({ "name": "text", "description": "text" });
db.templates.createIndex({ "category": 1 });
db.templates.createIndex({ "isPublic": 1 });

// 为bids集合创建索引
db.bids.createIndex({ "projectId": 1 });
db.bids.createIndex({ "type": 1 });
db.bids.createIndex({ "status": 1 });
db.bids.createIndex({ "createdAt": -1 });

// 插入初始数据
db.templates.insertMany([
  {
    name: "技术标准模板",
    category: "technical",
    description: "标准技术标书模板",
    content: {
      sections: [
        { id: "overview", name: "技术方案概述", required: true },
        { id: "companyProfile", name: "公司简介", required: true },
        { id: "projectUnderstanding", name: "项目理解", required: true },
        { id: "technicalSolution", name: "技术解决方案", required: true },
        { id: "implementationPlan", name: "实施计划", required: true },
        { id: "qualityAssurance", name: "质量保证", required: true }
      ]
    },
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: "商务标准模板",
    category: "commercial",
    description: "标准商务标书模板",
    content: {
      sections: [
        { id: "overview", name: "商务方案概述", required: true },
        { id: "pricing", name: "报价策略", required: true },
        { id: "costs", name: "成本分解", required: true },
        { id: "payment", name: "付款条件", required: true },
        { id: "contract", name: "合同条款", required: true }
      ]
    },
    isPublic: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

print('MongoDB初始化完成');