import React from 'react';
import { Link } from 'react-router-dom';
import { FileQuestion, Home } from 'lucide-react';
import Button from '../components/ui/Button';

const NotFound: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center px-4">
      <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-6">
        <FileQuestion size={48} className="text-blue-600" />
      </div>
      <h1 className="text-4xl font-bold text-gray-900 mb-2">404</h1>
      <h2 className="text-2xl font-semibold text-gray-700 mb-4">页面未找到</h2>
      <p className="text-gray-500 max-w-md mb-8">
        您请求的页面不存在或已被移除。请检查URL是否正确，或返回首页继续浏览。
      </p>
      <Link to="/">
        <Button size="lg">
          <Home size={18} className="mr-2" />
          返回首页
        </Button>
      </Link>
    </div>
  );
};

export default NotFound;