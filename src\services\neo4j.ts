import neo4j from 'neo4j-driver';

// Neo4j 连接配置
const NEO4J_URI = 'neo4j://localhost:7687';
const NEO4J_USER = 'neo4j';
const NEO4J_PASSWORD = 'password';

class Neo4jService {
  private driver: neo4j.Driver | null = null;

  async connect() {
    try {
      this.driver = neo4j.driver(
        NEO4J_URI,
        neo4j.auth.basic(NEO4J_USER, NEO4J_PASSWORD)
      );
      
      // 测试连接
      const session = this.driver.session();
      await session.run('RETURN 1');
      await session.close();
      
      console.log('Neo4j 连接成功');
      return true;
    } catch (error) {
      console.error('Neo4j 连接失败:', error);
      return false;
    }
  }

  async disconnect() {
    if (this.driver) {
      await this.driver.close();
      this.driver = null;
    }
  }

  // 创建文件节点
  async createFileNode(fileData: any) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      const result = await session.run(
        `CREATE (f:File {
          id: $id,
          name: $name,
          type: $type,
          size: $size,
          category: $category,
          uploadDate: $uploadDate,
          content: $content,
          extractedInfo: $extractedInfo
        }) RETURN f`,
        fileData
      );
      return result.records[0]?.get('f').properties;
    } finally {
      await session.close();
    }
  }

  // 创建项目节点
  async createProjectNode(projectData: any) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      const result = await session.run(
        `CREATE (p:Project {
          id: $id,
          name: $name,
          description: $description,
          client: $client,
          deadline: $deadline,
          status: $status,
          createdAt: $createdAt
        }) RETURN p`,
        projectData
      );
      return result.records[0]?.get('p').properties;
    } finally {
      await session.close();
    }
  }

  // 创建关系
  async createRelationship(fromId: string, toId: string, relationshipType: string, properties?: any) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      const query = properties 
        ? `MATCH (a), (b) WHERE a.id = $fromId AND b.id = $toId 
           CREATE (a)-[r:${relationshipType} $properties]->(b) RETURN r`
        : `MATCH (a), (b) WHERE a.id = $fromId AND b.id = $toId 
           CREATE (a)-[r:${relationshipType}]->(b) RETURN r`;
      
      const result = await session.run(query, { fromId, toId, properties });
      return result.records[0]?.get('r').properties;
    } finally {
      await session.close();
    }
  }

  // 自然语言查询
  async naturalLanguageQuery(query: string) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      // 这里可以结合AI将自然语言转换为Cypher查询
      // 暂时使用简单的关键词匹配
      let cypherQuery = '';
      
      if (query.includes('文件') || query.includes('file')) {
        cypherQuery = 'MATCH (f:File) RETURN f LIMIT 10';
      } else if (query.includes('项目') || query.includes('project')) {
        cypherQuery = 'MATCH (p:Project) RETURN p LIMIT 10';
      } else {
        cypherQuery = 'MATCH (n) RETURN n LIMIT 5';
      }
      
      const result = await session.run(cypherQuery);
      return result.records.map(record => record.toObject());
    } finally {
      await session.close();
    }
  }

  // 获取文件关联信息
  async getFileRelations(fileId: string) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      const result = await session.run(
        `MATCH (f:File {id: $fileId})-[r]-(related) 
         RETURN f, r, related`,
        { fileId }
      );
      return result.records.map(record => ({
        file: record.get('f').properties,
        relationship: record.get('r'),
        related: record.get('related').properties
      }));
    } finally {
      await session.close();
    }
  }

  // 分析招标文件内容并创建知识图谱
  async analyzeAndStoreDocument(fileId: string, content: string, extractedInfo: any) {
    if (!this.driver) throw new Error('Neo4j 未连接');
    
    const session = this.driver.session();
    try {
      // 创建文档节点
      await session.run(
        `MERGE (d:Document {id: $fileId})
         SET d.content = $content, d.extractedInfo = $extractedInfo`,
        { fileId, content, extractedInfo }
      );

      // 根据提取的信息创建相关节点
      if (extractedInfo.requirements) {
        for (const req of extractedInfo.requirements) {
          await session.run(
            `MERGE (r:Requirement {id: $reqId})
             SET r.content = $content, r.type = $type
             WITH r
             MATCH (d:Document {id: $fileId})
             MERGE (d)-[:HAS_REQUIREMENT]->(r)`,
            { 
              reqId: `${fileId}_req_${req.id}`,
              content: req.content,
              type: req.type,
              fileId 
            }
          );
        }
      }

      // 创建评分标准节点
      if (extractedInfo.scoringCriteria) {
        for (const criteria of extractedInfo.scoringCriteria) {
          await session.run(
            `MERGE (s:ScoringCriteria {id: $criteriaId})
             SET s.content = $content, s.weight = $weight
             WITH s
             MATCH (d:Document {id: $fileId})
             MERGE (d)-[:HAS_SCORING_CRITERIA]->(s)`,
            {
              criteriaId: `${fileId}_score_${criteria.id}`,
              content: criteria.content,
              weight: criteria.weight,
              fileId
            }
          );
        }
      }

      return true;
    } finally {
      await session.close();
    }
  }
}

export const neo4jService = new Neo4jService();
export default neo4jService;