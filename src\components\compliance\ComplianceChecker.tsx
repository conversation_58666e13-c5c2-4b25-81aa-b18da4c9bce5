import React, { useState } from 'react';
import { CheckCircle, AlertTriangle, XCircle, FileText, Target, Award } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { aiEnhancedService } from '../../services/aiEnhanced';

interface ComplianceResult {
  overallScore: number;
  sections: {
    name: string;
    score: number;
    status: 'excellent' | 'good' | 'warning' | 'error';
    issues: string[];
    suggestions: string[];
  }[];
  missingItems: {
    requirement: string;
    section: string;
    mandatory: boolean;
  }[];
  lastCheck: Date;
}

interface ComplianceCheckerProps {
  bidContent: any;
  requirements: any[];
  onComplianceUpdate?: (result: ComplianceResult) => void;
}

const ComplianceChecker: React.FC<ComplianceCheckerProps> = ({
  bidContent,
  requirements,
  onComplianceUpdate
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [result, setResult] = useState<ComplianceResult | null>(null);

  const handleCheck = async () => {
    setIsChecking(true);
    
    try {
      const response = await aiEnhancedService.checkCompliance(bidContent, requirements);
      
      if (response.success && response.data) {
        const complianceResult: ComplianceResult = {
          overallScore: response.data.overallScore,
          sections: [
            {
              name: '技术方案',
              score: 85,
              status: 'good',
              issues: response.data.issues.filter((i: any) => i.section === '技术方案').map((i: any) => i.issue),
              suggestions: response.data.suggestions.filter((s: any) => s.section === '技术方案').map((s: any) => s.suggestion)
            },
            {
              name: '实施计划',
              score: 75,
              status: 'warning',
              issues: response.data.issues.filter((i: any) => i.section === '实施计划').map((i: any) => i.issue),
              suggestions: response.data.suggestions.filter((s: any) => s.section === '实施计划').map((s: any) => s.suggestion)
            }
          ],
          missingItems: response.data.missingItems || [],
          lastCheck: new Date()
        };
        
        setResult(complianceResult);
        
        if (onComplianceUpdate) {
          onComplianceUpdate(complianceResult);
        }
      }
    } catch (error) {
      console.error('合规检查失败:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return <CheckCircle size={20} className="text-green-500" />;
      case 'good':
        return <CheckCircle size={20} className="text-blue-500" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-yellow-500" />;
      case 'error':
        return <XCircle size={20} className="text-red-500" />;
      default:
        return <FileText size={20} className="text-gray-500" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 90) return 'bg-green-100';
    if (score >= 80) return 'bg-blue-100';
    if (score >= 70) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-800">合规性检查</h3>
            <p className="text-gray-500">检查投标文件是否符合招标要求</p>
          </div>
          <Button
            onClick={handleCheck}
            disabled={isChecking}
          >
            {isChecking ? '检查中...' : '开始检查'}
          </Button>
        </div>

        {result && (
          <div className="space-y-6">
            {/* 总体评分 */}
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-24 h-24 rounded-full ${getScoreBgColor(result.overallScore)}`}>
                <span className={`text-3xl font-bold ${getScoreColor(result.overallScore)}`}>
                  {result.overallScore}
                </span>
              </div>
              <h4 className="mt-2 text-lg font-medium text-gray-800">总体合规评分</h4>
              <p className="text-sm text-gray-500">
                检查时间: {result.lastCheck.toLocaleString()}
              </p>
            </div>

            {/* 分项评分 */}
            <div>
              <h4 className="text-md font-medium text-gray-800 mb-4">分项评分</h4>
              <div className="space-y-4">
                {result.sections.map((section, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center">
                        {getStatusIcon(section.status)}
                        <span className="ml-2 font-medium text-gray-800">{section.name}</span>
                      </div>
                      <span className={`text-lg font-bold ${getScoreColor(section.score)}`}>
                        {section.score}/100
                      </span>
                    </div>
                    
                    {section.issues.length > 0 && (
                      <div className="mb-3">
                        <h5 className="text-sm font-medium text-red-700 mb-1">发现问题:</h5>
                        <ul className="text-sm text-red-600 space-y-1">
                          {section.issues.map((issue, issueIndex) => (
                            <li key={issueIndex} className="flex items-start">
                              <span className="mr-2">•</span>
                              <span>{issue}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {section.suggestions.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-blue-700 mb-1">改进建议:</h5>
                        <ul className="text-sm text-blue-600 space-y-1">
                          {section.suggestions.map((suggestion, suggestionIndex) => (
                            <li key={suggestionIndex} className="flex items-start">
                              <span className="mr-2">•</span>
                              <span>{suggestion}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 缺失项目 */}
            {result.missingItems.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                  <Target size={18} className="mr-2 text-red-500" />
                  缺失的必要内容
                </h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <ul className="space-y-2">
                    {result.missingItems.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <XCircle size={16} className="mr-2 text-red-500 mt-0.5" />
                        <div>
                          <span className="text-red-800 font-medium">{item.requirement}</span>
                          <span className="text-red-600 text-sm ml-2">({item.section})</span>
                          {item.mandatory && (
                            <span className="ml-2 px-2 py-0.5 text-xs bg-red-200 text-red-800 rounded">
                              必填项
                            </span>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* 合规建议 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-md font-medium text-blue-800 mb-2 flex items-center">
                <Award size={18} className="mr-2" />
                提升合规性建议
              </h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 确保所有必填项目都已完整填写</li>
                <li>• 技术方案应更加详细，包含具体的实现方案</li>
                <li>• 增加相关的成功案例和证明材料</li>
                <li>• 完善项目实施计划的时间安排</li>
              </ul>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ComplianceChecker;