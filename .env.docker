# Docker环境变量配置文件
NODE_ENV=production
PORT=3006

# 数据库配置
MONGODB_URI=*****************************************************
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123
POSTGRES_URI=*********************************************/ai_toubiao
REDIS_URI=redis://:password123@redis:6379

# AI服务配置
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_API_URL=https://api.deepseek.com/v1

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=104857600
MAX_FILES=10

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-super-secret-session-key-here

# 日志配置
LOG_LEVEL=info
LOG_DIR=/app/logs