import React, { useState } from 'react';
import { Bot, Save, Plus, Trash2 } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

interface CostItem {
  id: string;
  category: string;
  description: string;
  amount: number;
  unit: string;
  unitPrice: number;
  totalPrice: number;
}

const CostBreakdown: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);
  const [costItems, setCostItems] = useState<CostItem[]>([
    {
      id: '1',
      category: '人工成本',
      description: '高级开发工程师',
      amount: 2,
      unit: '人月',
      unitPrice: 30000,
      totalPrice: 60000
    },
    {
      id: '2',
      category: '设备成本',
      description: '服务器',
      amount: 1,
      unit: '台',
      unitPrice: 50000,
      totalPrice: 50000
    }
  ]);

  const addCostItem = () => {
    const newItem: CostItem = {
      id: Date.now().toString(),
      category: '',
      description: '',
      amount: 0,
      unit: '',
      unitPrice: 0,
      totalPrice: 0
    };
    setCostItems([...costItems, newItem]);
  };

  const removeCostItem = (id: string) => {
    setCostItems(costItems.filter(item => item.id !== id));
  };

  const updateCostItem = (id: string, field: keyof CostItem, value: string | number) => {
    setCostItems(costItems.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        if (field === 'amount' || field === 'unitPrice') {
          updatedItem.totalPrice = updatedItem.amount * updatedItem.unitPrice;
        }
        return updatedItem;
      }
      return item;
    }));
  };

  const totalCost = costItems.reduce((sum, item) => sum + item.totalPrice, 0);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">成本明细</h2>
            <p className="text-gray-500">详细列出项目各项成本</p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => setShowAiChat(true)}
            >
              <Bot size={16} className="mr-2" />
              AI辅助
            </Button>
            <Button>
              <Save size={16} className="mr-2" />
              保存
            </Button>
          </div>
        </div>

        <div className="space-y-6">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成本类别</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价(元)</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总价(元)</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {costItems.map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={item.category}
                        onChange={(e) => updateCostItem(item.id, 'category', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={item.description}
                        onChange={(e) => updateCostItem(item.id, 'description', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        className="w-24 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={item.amount}
                        onChange={(e) => updateCostItem(item.id, 'amount', parseFloat(e.target.value) || 0)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        className="w-24 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={item.unit}
                        onChange={(e) => updateCostItem(item.id, 'unit', e.target.value)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        className="w-32 px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={item.unitPrice}
                        onChange={(e) => updateCostItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.totalPrice.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => removeCostItem(item.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 size={16} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr className="bg-gray-50">
                  <td colSpan={5} className="px-6 py-4 text-right font-medium">总计：</td>
                  <td className="px-6 py-4 text-gray-900 font-medium">{totalCost.toLocaleString()}元</td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>

          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={addCostItem}
            >
              <Plus size={16} className="mr-2" />
              添加成本项
            </Button>
          </div>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="成本明细编制" 
        />
      )}
    </div>
  );
};

export default CostBreakdown;