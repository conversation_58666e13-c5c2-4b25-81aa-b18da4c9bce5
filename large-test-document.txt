This is a large test document for testing chunked upload functionality. The file upload queue has been implemented and the category mapping issues have been resolved. This file simulates a larger document that would trigger the chunked upload mechanism in the frontend. The upload modal now correctly maps frontend category values to backend enum values: bidding->招标文件, certificates->资质证书, cases->案例材料, technical->技术文档, other->其他. The progress tracking has been enhanced with better error handling and logging.
