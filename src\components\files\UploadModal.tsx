import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Upload, X, FileType, CheckCircle, AlertCircle, Pause, Play } from 'lucide-react';
import Button from '../ui/Button';
import { toast } from 'react-toastify';
import { fileApi } from '../../services/api';

interface UploadModalProps {
  onClose: () => void;
  onUploadSuccess?: () => void;
}

type FileCategory = 'bidding' | 'certificates' | 'cases' | 'technical' | 'other';

const categoryMap = {
  'bidding': '招标文件',
  'certificates': '资质证书',
  'cases': '案例材料',
  'technical': '技术文档',
  'other': '其他'
};

interface FileWithStatus {
  file: File;
  status: 'pending' | 'uploading' | 'success' | 'error' | 'paused';
  progress: number;
  error?: string;
  uploadedBytes?: number;
  abortController?: AbortController;
}

const UploadModal: React.FC<UploadModalProps> = ({ onClose, onUploadSuccess }) => {
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [category, setCategory] = useState<FileCategory>('other');
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadQueue, setUploadQueue] = useState<number>(0);
  const [successCount, setSuccessCount] = useState(0);
  
  // 使用 ref 来跟踪最新的文件状态
  const filesRef = useRef<FileWithStatus[]>([]);
  
  // 同步 files 状态到 ref
  useEffect(() => {
    filesRef.current = files;
  }, [files]);

  // 组件卸载时取消所有上传
  useEffect(() => {
    return () => {
      // 取消所有正在进行的上传
      files.forEach(file => {
        if (file.abortController) {
          file.abortController.abort();
        }
      });
    };
  }, [files]);

  // 支持的文件类型
  const supportedTypes = [
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.txt', '.jpg', '.jpeg', '.png', '.gif', '.zip', '.rar'
  ];

  // 文件大小限制 (100MB)
  const MAX_FILE_SIZE = 100 * 1024 * 1024;
  const CHUNK_SIZE = 1024 * 1024; // 1MB chunks for large files

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      addFiles(Array.from(e.target.files));
    }
  }, []);

  const addFiles = useCallback((newFiles: File[]) => {
    console.log('尝试添加文件:', newFiles.map(f => ({ 
      name: f.name, 
      size: f.size, 
      type: f.type,
      extension: '.' + f.name.split('.').pop()?.toLowerCase()
    })));
    console.log('支持的文件类型:', supportedTypes);

    const validFiles = newFiles.filter(file => {
      // 检查文件大小
      if (file.size > MAX_FILE_SIZE) {
        console.error(`文件大小超限: ${file.name} (${file.size} bytes > ${MAX_FILE_SIZE} bytes)`);
        toast.error(`文件 ${file.name} 超过100MB限制`);
        return false;
      }
      
      // 检查文件类型
      const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!supportedTypes.includes(fileExt)) {
        console.error(`不支持的文件类型: ${file.name}, 扩展名: ${fileExt}, MIME类型: ${file.type}`);
        toast.error(`不支持的文件类型: ${file.name}`);
        return false;
      }
      
      console.log(`文件验证通过: ${file.name}`);
      return true;
    });

    const filesWithStatus: FileWithStatus[] = validFiles.map(file => ({
      file,
      status: 'pending',
      progress: 0,
      uploadedBytes: 0
    }));

    setFiles(prev => {
      const newFiles = [...prev, ...filesWithStatus];
      filesRef.current = newFiles;
      return newFiles;
    });
    
    if (validFiles.length > 0) {
      toast.success(`已添加 ${validFiles.length} 个文件到上传队列`);
    }
  }, []);

  const handleRemoveFile = useCallback((index: number) => {
    setFiles(prev => {
      const newFiles = [...prev];
      const file = newFiles[index];
      
      // 如果文件正在上传，取消上传
      if (file.status === 'uploading' && file.abortController) {
        file.abortController.abort();
      }
      
      newFiles.splice(index, 1);
      filesRef.current = newFiles;
      return newFiles;
    });
  }, []);

  const pauseUpload = useCallback((index: number) => {
    setFiles(prev => {
      const newFiles = prev.map((file, i) => {
        if (i === index && file.status === 'uploading' && file.abortController) {
          file.abortController.abort();
          return { ...file, status: 'paused' };
        }
        return file;
      });
      filesRef.current = newFiles;
      return newFiles;
    });
  }, []);

  const resumeUpload = useCallback((index: number) => {
    const file = files[index];
    if (file && file.status === 'paused') {
      uploadSingleFile(index);
    }
  }, [files]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files) {
      addFiles(Array.from(e.dataTransfer.files));
    }
  }, [addFiles]);

  // 分块上传大文件
  const uploadFileInChunks = async (file: File, index: number): Promise<void> => {
    const abortController = new AbortController();
    
    // 更新文件状态
    setFiles(prev => {
      const newFiles = prev.map((f, i) => 
        i === index 
          ? { ...f, status: 'uploading' as const, abortController, progress: 0 }
          : f
      );
      filesRef.current = newFiles;
      return newFiles;
    });

    try {
      // 如果文件小于5MB，直接上传
      if (file.size < 5 * 1024 * 1024) {
        console.log(`上传小文件: ${file.name}, 大小: ${file.size}`);
        await uploadSmallFile(file, index, abortController);
        return;
      }

      console.log(`开始分块上传大文件: ${file.name}, 大小: ${file.size}`);
      
      // 大文件分块上传
      const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
      let uploadedBytes = 0;

      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        if (abortController.signal.aborted) {
          throw new Error('Upload cancelled');
        }

        const start = chunkIndex * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, file.size);
        const chunk = file.slice(start, end);

        console.log(`上传块 ${chunkIndex + 1}/${totalChunks}, 大小: ${chunk.size}`);

        const formData = new FormData();
        formData.append('chunk', chunk);
        formData.append('chunkIndex', chunkIndex.toString());
        formData.append('totalChunks', totalChunks.toString());
        formData.append('fileName', file.name);
        formData.append('category', category);

        const response = await fetch('/api/files/upload-chunk', {
          method: 'POST',
          body: formData,
          signal: abortController.signal
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error(`分块上传失败:`, {
            status: response.status,
            statusText: response.statusText,
            errorData,
            fileName: file.name,
            chunkIndex,
            totalChunks,
            chunkSize: chunk.size,
            category
          });
          throw new Error(errorData.message || `Chunk upload failed: ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`块 ${chunkIndex + 1} 上传结果:`, result);

        uploadedBytes += chunk.size;
        const progress = Math.round((uploadedBytes / file.size) * 100);

        // 更新进度
        setFiles(prev => {
          const newFiles = prev.map((f, i) => 
            i === index 
              ? { ...f, progress, uploadedBytes }
              : f
          );
          filesRef.current = newFiles;
          return newFiles;
        });

        // 如果是最后一块且服务器返回completed，则标记为成功
        if (result.completed) {
          console.log(`文件 ${file.name} 上传完成`);
          setFiles(prev => {
            const newFiles = prev.map((f, i) => 
              i === index 
                ? { ...f, status: 'success' as const, progress: 100 }
                : f
            );
            // 同步更新 ref
            filesRef.current = newFiles;
            // 增加成功计数
            setSuccessCount(prev => prev + 1);
            return newFiles;
          });
          return;
        }

        // 添加小延迟避免阻塞UI
        await new Promise(resolve => setTimeout(resolve, 50));
      }

    } catch (error: any) {
      console.error(`文件 ${file.name} 上传失败:`, error);
      
      if (error.name === 'AbortError' || error.message === 'Upload cancelled') {
        // 用户取消上传
        setFiles(prev => {
          const newFiles = prev.map((f, i) => 
            i === index 
              ? { ...f, status: 'paused' as const }
              : f
          );
          filesRef.current = newFiles;
          return newFiles;
        });
      } else {
        // 上传错误
        setFiles(prev => prev.map((f, i) => 
          i === index 
            ? { ...f, status: 'error' as const, error: error.message || '上传失败' }
            : f
        ));
      }
    }
  };

  // 小文件直接上传
  const uploadSmallFile = async (file: File, index: number, abortController: AbortController): Promise<void> => {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('category', category);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map((f, i) => {
          if (i === index && f.status === 'uploading') {
            const newProgress = Math.min(f.progress + 10, 90);
            return { ...f, progress: newProgress };
          }
          return f;
        }));
      }, 200);

      console.log(`开始上传文件: ${file.name}, 大小: ${file.size}, 类型: ${file.type}, 类别: ${category}`);
      
      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData,
        signal: abortController.signal
      });

      clearInterval(progressInterval);

      console.log(`上传响应状态: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`上传失败详情:`, {
          status: response.status,
          statusText: response.statusText,
          errorData,
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          category
        });
        throw new Error(errorData.message || `Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Small file upload response:', result);

      console.log(`文件 ${file.name} 上传成功，更新状态为 success`);
      setFiles(prev => {
        console.log('当前文件列表:', prev.map(f => ({ name: f.file.name, status: f.status })));
        const newFiles = prev.map((f, i) => {
          if (i === index) {
            console.log(`设置文件 ${f.file.name} 状态为 success`);
            return { ...f, status: 'success' as const, progress: 100 };
          }
          return f;
        });
        console.log('更新后的文件列表:', newFiles.map(f => ({ name: f.file.name, status: f.status })));
        // 同步更新 ref
        filesRef.current = newFiles;
        // 增加成功计数
        setSuccessCount(prev => prev + 1);
        return newFiles;
      });
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Small file upload error:', error);
        throw error;
      }
    }
  };

  const uploadSingleFile = async (index: number) => {
    const fileWithStatus = files[index];
    if (!fileWithStatus || fileWithStatus.status === 'uploading') {
      return;
    }

    try {
      await uploadFileInChunks(fileWithStatus.file, index);
    } catch (error) {
      console.error(`上传文件 ${fileWithStatus.file.name} 失败:`, error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (files.length === 0) {
      toast.error('请选择要上传的文件');
      return;
    }

    // 测试后端连接
    try {
      console.log('测试后端连接...');
      const healthResponse = await fetch('/api/health');
      console.log('后端健康检查:', healthResponse.status, healthResponse.statusText);
      
      if (!healthResponse.ok) {
        toast.error('后端服务器连接失败，请检查服务器是否正在运行');
        return;
      }
    } catch (error) {
      console.error('后端连接测试失败:', error);
      toast.error('无法连接到后端服务器');
      return;
    }
    
    setIsUploading(true);
    setUploadQueue(0);
    setSuccessCount(0); // 重置成功计数器
    
    try {
      // 逐个上传文件，避免同时上传过多文件
      for (let i = 0; i < files.length; i++) {
        // 使用 ref 获取实时状态
        const currentFile = filesRef.current[i];
        console.log(`处理文件 ${i + 1}/${files.length}: ${currentFile.file.name}, 当前状态: ${currentFile.status}`);
        
        if (currentFile.status === 'pending' || currentFile.status === 'error') {
          setUploadQueue(i + 1);
          console.log(`开始上传文件: ${currentFile.file.name}`);
          await uploadSingleFile(i);
          
          // 检查上传后的状态 - 需要短暂延迟让状态更新
          await new Promise(resolve => setTimeout(resolve, 100));
          console.log(`上传完成后等待状态更新...`);
          
          // 添加延迟避免服务器压力
          await new Promise(resolve => setTimeout(resolve, 500));
        } else {
          console.log(`跳过文件 ${currentFile.file.name}，状态: ${currentFile.status}`);
        }
      }
      
      const totalFiles = files.length;
      
      console.log(`上传结果: ${successCount}/${totalFiles} 个文件成功`);
      
      if (successCount > 0) {
        toast.success(`成功上传 ${successCount} 个文件`);
        
        if (successCount === totalFiles) {
          // 所有文件都成功上传，延迟关闭
          console.log('所有文件上传完成，准备关闭对话框');
          setTimeout(() => {
            onUploadSuccess?.();
            onClose();
          }, 2000);
          
          // 不在finally中重置状态，让用户看到完成状态
          return;
        } else {
          // 部分文件成功，停止上传状态但不关闭对话框
          setIsUploading(false);
          setUploadQueue(0);
        }
      } else {
        // 没有文件成功上传
        setIsUploading(false);
        setUploadQueue(0);
      }
      
    } catch (error: any) {
      console.error('批量上传错误:', error);
      toast.error('部分文件上传失败，请检查网络连接后重试');
      
      setIsUploading(false);
      setUploadQueue(0);
    }
  };

  // 获取文件大小的可读格式
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'error':
        return <AlertCircle size={16} className="text-red-500" />;
      case 'uploading':
        return <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      case 'paused':
        return <Pause size={16} className="text-yellow-500" />;
      default:
        return <FileType size={16} className="text-gray-400" />;
    }
  };

  const canUpload = files.length > 0 && !isUploading;
  const hasErrors = files.some(f => f.status === 'error');
  const allCompleted = files.length > 0 && successCount === files.length;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        <div className="border-b px-6 py-4 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">文件上传</h3>
            {isUploading && (
              <p className="text-sm text-gray-500">
                正在上传第 {uploadQueue} 个文件，共 {files.length} 个
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
            disabled={isUploading && !allCompleted}
          >
            <X size={20} />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
          <div className="p-6 flex-1 overflow-y-auto">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文件类别
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value as FileCategory)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                disabled={isUploading}
              >
                <option value="bidding">招标文件</option>
                <option value="certificates">资质证书</option>
                <option value="cases">案例材料</option>
                <option value="technical">技术文档</option>
                <option value="other">其他</option>
              </select>
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                上传文件
              </label>
              <div 
                className={`relative border-2 border-dashed rounded-md p-6 flex flex-col items-center justify-center cursor-pointer transition-colors ${
                  dragActive 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onClick={() => !isUploading && document.getElementById('file-upload')?.click()}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className={`h-12 w-12 ${dragActive ? 'text-blue-500' : 'text-gray-400'}`} />
                <p className="mt-2 text-sm text-gray-500">
                  拖拽文件到此处，或点击选择文件
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  支持: {supportedTypes.join(', ')} | 单文件最大 100MB | 最多10个文件
                </p>
                <p className="mt-1 text-xs text-blue-600">
                  大文件将自动分块上传，支持断点续传
                </p>
                <input
                  id="file-upload"
                  type="file"
                  multiple
                  accept={supportedTypes.join(',')}
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={isUploading}
                />
              </div>
            </div>
            
            {files.length > 0 && (
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">
                    已选择的文件 ({files.length})
                  </h4>
                  {hasErrors && (
                    <button
                      type="button"
                      onClick={() => {
                        files.forEach((file, index) => {
                          if (file.status === 'error') {
                            uploadSingleFile(index);
                          }
                        });
                      }}
                      className="text-sm text-blue-600 hover:text-blue-800"
                      disabled={isUploading}
                    >
                      重试失败文件
                    </button>
                  )}
                </div>
                
                <div className="space-y-3 max-h-80 overflow-y-auto">
                  {files.map((fileWithStatus, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between py-3 px-4 rounded-md border ${
                        fileWithStatus.status === 'success' 
                          ? 'bg-green-50 border-green-200' 
                          : fileWithStatus.status === 'error'
                          ? 'bg-red-50 border-red-200'
                          : fileWithStatus.status === 'uploading'
                          ? 'bg-blue-50 border-blue-200'
                          : fileWithStatus.status === 'paused'
                          ? 'bg-yellow-50 border-yellow-200'
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex items-center flex-1 min-w-0">
                        {getStatusIcon(fileWithStatus.status)}
                        <div className="ml-3 flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-700 truncate">
                            {fileWithStatus.file.name}
                          </p>
                          <div className="flex items-center justify-between mt-1">
                            <div className="flex items-center space-x-2">
                              <p className="text-xs text-gray-500">
                                {formatFileSize(fileWithStatus.file.size)}
                              </p>
                              {fileWithStatus.status === 'uploading' && fileWithStatus.uploadedBytes && (
                                <p className="text-xs text-blue-600">
                                  {formatFileSize(fileWithStatus.uploadedBytes)} / {formatFileSize(fileWithStatus.file.size)}
                                </p>
                              )}
                            </div>
                            
                            {fileWithStatus.status === 'uploading' && (
                              <p className="text-xs text-blue-600">
                                {fileWithStatus.progress}%
                              </p>
                            )}
                            
                            {fileWithStatus.status === 'error' && fileWithStatus.error && (
                              <p className="text-xs text-red-600 truncate max-w-xs">
                                {fileWithStatus.error}
                              </p>
                            )}
                          </div>
                          
                          {/* 进度条 */}
                          {(fileWithStatus.status === 'uploading' || fileWithStatus.status === 'paused') && (
                            <div className="mt-2 w-full bg-gray-200 rounded-full h-1.5">
                              <div
                                className={`h-1.5 rounded-full transition-all duration-300 ${
                                  fileWithStatus.status === 'paused' ? 'bg-yellow-500' : 'bg-blue-600'
                                }`}
                                style={{ width: `${fileWithStatus.progress}%` }}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        {fileWithStatus.status === 'uploading' && (
                          <button
                            type="button"
                            onClick={() => pauseUpload(index)}
                            className="text-yellow-600 hover:text-yellow-800"
                            title="暂停上传"
                          >
                            <Pause size={16} />
                          </button>
                        )}
                        
                        {fileWithStatus.status === 'paused' && (
                          <button
                            type="button"
                            onClick={() => resumeUpload(index)}
                            className="text-blue-600 hover:text-blue-800"
                            title="继续上传"
                          >
                            <Play size={16} />
                          </button>
                        )}
                        
                        {(fileWithStatus.status === 'pending' || fileWithStatus.status === 'error' || fileWithStatus.status === 'paused') && (
                          <button
                            type="button"
                            onClick={() => handleRemoveFile(index)}
                            className="text-gray-400 hover:text-red-500"
                            title="移除文件"
                          >
                            <X size={16} />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="px-6 py-4 bg-gray-50 border-t flex justify-between items-center rounded-b-lg">
            <div className="text-sm text-gray-500">
              {files.length > 0 && (
                <span>
                  {files.filter(f => f.status === 'success').length} / {files.length} 完成
                  {isUploading && ` (正在处理第 ${uploadQueue} 个)`}
                </span>
              )}
            </div>
            
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isUploading && !allCompleted}
              >
                {allCompleted ? '完成' : isUploading ? '上传中...' : '取消'}
              </Button>
              
              {!allCompleted && (
                <Button
                  type="submit"
                  disabled={!canUpload}
                >
                  {isUploading ? '上传中...' : `上传 ${files.length} 个文件`}
                </Button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UploadModal;