import React, { useState, useRef, useEffect } from 'react';
import { X, Send, Bot, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../ui/Button';
import { v4 as uuidv4 } from 'uuid';
import ReactMarkdown from 'react-markdown';

interface AiChatProps {
  onClose: () => void;
  context: string;
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  type?: 'text' | 'error' | 'system';
  isStreaming?: boolean;
}

const AiChat: React.FC<AiChatProps> = ({ onClose, context }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [chatHistory, setChatHistory] = useState<Message[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    // 添加初始AI消息
    const initialMessage: Message = {
      id: uuidv4(),
      content: `您好，我是您的AI助手。我可以帮助您完成${context}工作，请问有什么我可以协助您的？`,
      sender: 'ai',
      timestamp: new Date()
    };
    setMessages([initialMessage]);
  }, [context]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || isLoading) return;
    
    const userMessage: Message = {
      id: uuidv4(),
      content: input.trim(),
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    };
    
    const currentMessages = [...messages, userMessage];
    setMessages(currentMessages);
    setChatHistory(currentMessages);
    
    const currentInput = input.trim();
    setInput('');
    setIsLoading(true);
    
    // 聚焦回输入框
    inputRef.current?.focus();
    
    try {
      // 准备发送给API的消息历史（最近10条消息）
      const recentMessages = currentMessages
        .filter(msg => msg.type !== 'error' && msg.type !== 'system')
        .slice(-10)
        .map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentInput,
          messages: recentMessages,
          context: context
        }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success && data.message) {
        const aiResponse: Message = {
          id: uuidv4(),
          content: data.message,
          sender: 'ai',
          timestamp: new Date(),
          type: 'text'
        };
        
        const updatedMessages = [...currentMessages, aiResponse];
        setMessages(updatedMessages);
        setChatHistory(updatedMessages);
      } else {
        throw new Error(data.message || 'AI响应格式错误');
      }
    } catch (error) {
      console.error('AI响应错误:', error);
      
      let errorText = '抱歉，我遇到了一些问题。';
      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          errorText = '网络连接问题，请检查您的网络连接后重试。';
        } else if (error.message.includes('HTTP 5')) {
          errorText = '服务器暂时不可用，请稍后再试。';
        } else if (error.message.includes('HTTP 4')) {
          errorText = '请求格式错误，请重新输入您的问题。';
        } else {
          errorText = `${errorText} 错误信息：${error.message}`;
        }
      }
      
      const errorMessage: Message = {
        id: uuidv4(),
        content: errorText,
        sender: 'ai',
        timestamp: new Date(),
        type: 'error'
      };
      
      const updatedMessages = [...currentMessages, errorMessage];
      setMessages(updatedMessages);
    } finally {
      setIsLoading(false);
    }
  };

  // 添加快捷键支持
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  // 清空聊天记录
  const clearChat = () => {
    setMessages([]);
    setChatHistory([]);
    setInput('');
    
    // 重新添加初始消息
    const initialMessage: Message = {
      id: uuidv4(),
      content: `您好，我是您的AI助手。我可以帮助您完成${context}工作，请问有什么我可以协助您的？`,
      sender: 'ai',
      timestamp: new Date(),
      type: 'system'
    };
    setMessages([initialMessage]);
  };

  // 模拟AI响应内容
  const getAiResponse = (message: string, context: string): string => {
    if (context === '技术标制作') {
      if (message.includes('公司简介') || message.includes('公司介绍')) {
        return `公司简介是技术标中的重要部分，应包含以下要素：

1. **公司基本信息**：
   - 成立时间、注册资本
   - 企业性质和规模
   - 主要业务范围

2. **资质证书**：
   - 相关行业资质等级
   - 重要认证（如ISO9001、CMMI等）
   - 专利和知识产权

3. **成功案例**：
   - 类似项目经验
   - 重点客户名单
   - 项目成果和效益

建议重点突出与本次投标项目相关的能力和经验，展示公司的技术实力和行业地位。

您需要我帮您起草公司简介的初稿吗？`;
      } else if (message.includes('技术方案') || message.includes('解决方案')) {
        return `技术方案是技术标的核心部分，应该包含以下内容：

1. **总体架构**：
   - 系统架构图
   - 技术框架选择
   - 各模块关系说明

2. **功能设计**：
   - 按招标要求逐一响应
   - 详细的功能描述
   - 创新点和亮点

3. **技术特点**：
   - 采用的关键技术
   - 性能和安全保障
   - 扩展性和兼容性

4. **产品参数**：
   - 硬件配置要求
   - 软件环境需求
   - 性能指标

建议使用图表结合文字说明，使方案更加直观清晰。针对招标文件中的技术要求，应逐条进行响应，确保不遗漏任何要点。

需要我协助您起草某个具体部分的内容吗？`;
      } else if (message.includes('实施计划') || message.includes('进度')) {
        return `项目实施计划应当包括以下要素：

1. **项目团队**：
   - 组织结构图
   - 关键人员及职责
   - 人员资质和经验

2. **进度安排**：
   - 项目里程碑
   - 甘特图或时间表
   - 各阶段工作内容

3. **风险管理**：
   - 可能的风险点识别
   - 应对策略和预案
   - 风险控制措施

4. **资源配置**：
   - 人力资源分配
   - 设备和工具准备
   - 外部资源协调

建议将整个项目分为需求分析、设计开发、测试验收、部署上线等阶段，并明确各阶段的时间节点和交付物。

您希望我帮您制定详细的进度计划表吗？`;
      } else {
        return `关于${context}，我建议您可以从以下几个方面着手：

1. 首先分析招标文件中的技术要求，确保理解所有要点
2. 根据公司实际情况，准备符合要求的资质和案例材料
3. 设计技术方案时，既要满足基本需求，又要体现公司的技术优势
4. 实施方案要详细可行，包括人员、进度和质量保证措施
5. 文档格式要规范，语言表述要专业清晰

您有什么具体的问题需要我帮助解答吗？`;
      }
    } else {
      return `我了解您关于"${message}"的咨询。在${context}过程中，这是一个重要的考虑点。建议您可以从以下几个方面着手分析和解决这个问题。

如果您需要更具体的建议，请告诉我更多关于您项目的细节，我可以提供更有针对性的帮助。`;
    }
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl h-[600px] flex flex-col"
      >
        {/* 头部 */}
        <div className="border-b px-6 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <Bot size={20} className="text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">AI助手</h3>
            <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
              {context}
            </span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`max-w-[80%] ${
                  message.sender === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : message.type === 'error' 
                      ? 'bg-red-50 border border-red-200 text-red-800'
                      : message.type === 'system'
                        ? 'bg-green-50 border border-green-200 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                } rounded-lg px-4 py-3`}>
                  {message.sender === 'ai' && message.type !== 'error' && message.type !== 'system' && (
                    <div className="flex items-center mb-2">
                      <Bot size={16} className="text-blue-600 mr-2" />
                      <span className="text-xs font-medium text-blue-600">AI助手</span>
                    </div>
                  )}
                  
                  <div className={`prose prose-sm max-w-none ${message.sender === 'user' ? 'prose-invert' : ''}`}>
                    {message.sender === 'user' ? (
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    ) : (
                      <ReactMarkdown
                        className={message.type === 'error' ? 'text-red-700' : message.type === 'system' ? 'text-green-700' : ''}
                      >
                        {message.content}
                      </ReactMarkdown>
                    )}
                  </div>
                  
                  <div className={`text-xs mt-2 ${
                    message.sender === 'user' 
                      ? 'text-blue-200' 
                      : message.type === 'error'
                        ? 'text-red-500'
                        : message.type === 'system'
                          ? 'text-green-500'
                          : 'text-gray-500'
                  } text-right`}>
                    {formatTime(message.timestamp)}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 rounded-lg px-4 py-3 flex items-center">
                <Loader2 size={16} className="text-blue-600 animate-spin mr-2" />
                <span className="text-sm text-gray-500">AI正在思考...</span>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        {/* 输入区域 */}
        <div className="border-t bg-gray-50 p-4">
          {/* 快捷操作按钮 */}
          <div className="mb-3 flex flex-wrap gap-2">
            <button
              onClick={() => setInput('请帮我制定技术方案大纲')}
              className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200 transition-colors"
              disabled={isLoading}
            >
              技术方案
            </button>
            <button
              onClick={() => setInput('如何编写项目实施计划？')}
              className="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-full hover:bg-green-200 transition-colors"
              disabled={isLoading}
            >
              实施计划
            </button>
            <button
              onClick={() => setInput('帮我分析投标文件的关键要点')}
              className="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
              disabled={isLoading}
            >
              要点分析
            </button>
            <button
              onClick={clearChat}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
              disabled={isLoading}
            >
              清空对话
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="flex items-end space-x-2">
            <div className="flex-1">
              <textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="输入您的问题... (Shift+Enter换行，Enter发送)"
                className="w-full border border-gray-300 rounded-md px-4 py-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[44px] max-h-[120px]"
                disabled={isLoading}
                rows={1}
                style={{ height: 'auto' }}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement;
                  target.style.height = 'auto';
                  target.style.height = Math.min(target.scrollHeight, 120) + 'px';
                }}
              />
            </div>
            <Button
              type="submit"
              disabled={isLoading || !input.trim()}
              className="h-[44px] px-4"
            >
              {isLoading ? (
                <Loader2 size={18} className="animate-spin" />
              ) : (
                <Send size={18} />
              )}
            </Button>
          </form>
          
          <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
            <span>Shift+Enter换行 • Enter发送</span>
            <span>{messages.length > 0 && `共 ${messages.length} 条消息`}</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default AiChat;