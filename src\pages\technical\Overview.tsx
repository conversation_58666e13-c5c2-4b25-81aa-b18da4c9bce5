import React, { useState } from 'react';
import { ArrowRight, FileText, Upload, Bot } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const TechnicalOverview: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);
  
  const completionStatus = [
    { section: '公司简介', completion: 30 },
    { section: '项目理解', completion: 20 },
    { section: '技术方案', completion: 10 },
    { section: '实施计划', completion: 0 },
    { section: '质量保证', completion: 0 },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">技术标制作概览</h2>
            <p className="text-gray-600 mb-6">
              技术标是投标文件的重要组成部分，主要展示投标人的技术实力、对项目的理解以及提供的技术解决方案。
              通过以下步骤，系统将帮助您制作一份专业、符合要求的技术标文件。
            </p>
            
            <div className="space-y-4">
              {completionStatus.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-700">{item.section}</span>
                    <span className="text-sm text-gray-500">{item.completion}%</span>
                  </div>
                  <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-600 rounded-full" 
                      style={{ width: `${item.completion}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 flex flex-wrap gap-3">
              <Button 
                variant="primary"
                onClick={() => setShowAiChat(true)}
              >
                <Bot size={16} className="mr-2" />
                AI辅助编写
              </Button>
              <Button variant="outline">
                <FileText size={16} className="mr-2" />
                预览文档
              </Button>
            </div>
          </div>
          
          <div className="w-full md:w-64 shrink-0">
            <Card className="bg-gray-50 border-dashed">
              <div className="text-center space-y-4">
                <div className="flex justify-center">
                  <Upload className="h-10 w-10 text-gray-400" />
                </div>
                <h3 className="text-sm font-medium text-gray-700">上传招标文件</h3>
                <p className="text-xs text-gray-500">
                  上传招标文件，AI将自动分析并提取关键信息
                </p>
                <Button size="sm" fullWidth>上传文件</Button>
              </div>
            </Card>
          </div>
        </div>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold text-gray-800 mb-4">技术标制作流程</h2>
        
        <div className="space-y-6">
          <div className="relative pb-6 pl-6 border-l-2 border-blue-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-600"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">1. 上传和分析招标文件</h3>
            <p className="text-sm text-gray-600">
              上传招标文件，系统将通过AI分析招标要求，提取技术标需要响应的关键点和要求。
            </p>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-blue-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-500"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">2. 完善公司资质和优势</h3>
            <p className="text-sm text-gray-600">
              根据招标文件要求，编写公司简介，重点突出与项目相关的资质、经验和优势。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-blue-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-400"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">3. 项目理解与需求分析</h3>
            <p className="text-sm text-gray-600">
              阐述对项目的理解，分析项目需求和特点，展示对项目的深入了解。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-blue-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-300"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">4. 技术解决方案</h3>
            <p className="text-sm text-gray-600">
              根据项目需求，提供详细的技术解决方案，包括架构设计、技术选型、功能实现等。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pb-6 pl-6 border-l-2 border-blue-200">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-200"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">5. 项目实施计划</h3>
            <p className="text-sm text-gray-600">
              制定详细的项目实施计划，包括团队组织、时间安排、工作分解等。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
          
          <div className="relative pl-6">
            <div className="absolute left-[-8px] top-0 w-4 h-4 rounded-full bg-blue-100"></div>
            <h3 className="text-md font-medium text-gray-800 mb-2">6. 质量保证措施</h3>
            <p className="text-sm text-gray-600">
              说明项目质量保证体系和具体措施，确保项目成果符合要求。
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              <ArrowRight size={14} className="mr-1" />
              前往编辑
            </Button>
          </div>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="技术标制作" 
        />
      )}
    </div>
  );
};

export default TechnicalOverview;