import React, { useState } from 'react';
import RichTextEditor from '../../components/editor/RichTextEditor';
import TemplateLibrary from '../../components/templates/TemplateLibrary';
import DocumentExporter from '../../components/export/DocumentExporter';
import CollaborationPanel from '../../components/collaboration/CollaborationPanel';
import { Users, BookTemplate as Template, Download } from 'lucide-react';
import Button from '../../components/ui/Button';

const CompanyProfile: React.FC = () => {
  const [content, setContent] = useState('');
  const [showTemplates, setShowTemplates] = useState(false);
  const [showCollaboration, setShowCollaboration] = useState(false);
  const [showExporter, setShowExporter] = useState(false);
  
  // 模拟招标文件数据
  const documentData = {
    requirements: [
      {
        id: 'req_001',
        content: '投标人应具有软件开发相关资质',
        category: '资质要求',
        mandatory: true
      },
      {
        id: 'req_002', 
        content: '投标人应具有3年以上同类项目经验',
        category: '经验要求',
        mandatory: true
      },
      {
        id: 'req_003',
        content: '投标人应提供详细的公司简介和团队介绍',
        category: '文档要求',
        mandatory: true
      }
    ]
  };

  // 模拟当前用户
  const currentUser = {
    id: 'user_001',
    name: '张三',
    email: '<EMAIL>',
    role: 'owner' as const,
    status: 'online' as const,
    lastSeen: '刚刚',
    permissions: {
      canEdit: true,
      canComment: true,
      canShare: true,
      canExport: true
    }
  };

  const handleSelectTemplate = (template: any) => {
    setContent(template.content);
    setShowTemplates(false);
  };

  const handleExport = (format: string) => {
    console.log(`导出为 ${format} 格式`);
  };

  return (
    <div className="space-y-6">
      {/* 工具栏 */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-800">公司简介</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTemplates(true)}
          >
            <Template size={16} className="mr-1" />
            模板
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCollaboration(true)}
          >
            <Users size={16} className="mr-1" />
            协作
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExporter(true)}
          >
            <Download size={16} className="mr-1" />
            导出
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 主编辑区域 */}
        <div className="lg:col-span-3">
          <RichTextEditor
            title="公司简介"
            content={content}
            onChange={setContent}
            documentData={documentData}
            requirements={documentData.requirements}
            onSave={() => console.log('保存公司简介:', content)}
            onExport={handleExport}
            isCollaborative={true}
            collaborators={[
              { name: '李四', status: 'online', role: '编辑者' },
              { name: '王五', status: 'away', role: '查看者' }
            ]}
          />
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          {showCollaboration && (
            <CollaborationPanel
              documentId="company-profile"
              currentUser={currentUser}
            />
          )}
        </div>
      </div>

      {/* 模板库模态框 */}
      {showTemplates && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <div className="border-b px-6 py-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">选择模板</h3>
              <button
                onClick={() => setShowTemplates(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                ×
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <TemplateLibrary
                category="technical"
                onSelectTemplate={handleSelectTemplate}
              />
            </div>
          </div>
        </div>
      )}

      {/* 导出器模态框 */}
      {showExporter && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
            <div className="border-b px-6 py-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">导出文档</h3>
              <button
                onClick={() => setShowExporter(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                ×
              </button>
            </div>
            <div className="p-6">
              <DocumentExporter
                content={content}
                title="公司简介"
                onExport={handleExport}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyProfile;