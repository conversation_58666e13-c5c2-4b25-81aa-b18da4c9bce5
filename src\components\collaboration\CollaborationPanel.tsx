import React, { useState, useEffect } from 'react';
import { Users, MessageCircle, Share2, UserPlus, Settings, Crown, Eye, Edit3 } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { toast } from 'react-toastify';

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away';
  lastSeen: string;
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canShare: boolean;
    canExport: boolean;
  };
}

interface Comment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: string;
  position?: {
    section: string;
    paragraph: number;
  };
  resolved: boolean;
  replies: Comment[];
}

interface CollaborationPanelProps {
  documentId: string;
  currentUser: Collaborator;
  onInviteUser?: (email: string, role: string) => void;
  onUpdatePermissions?: (userId: string, permissions: any) => void;
}

const CollaborationPanel: React.FC<CollaborationPanelProps> = ({
  documentId,
  currentUser,
  onInviteUser,
  onUpdatePermissions
}) => {
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [activeTab, setActiveTab] = useState<'collaborators' | 'comments' | 'activity'>('collaborators');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'viewer'>('viewer');
  const [newComment, setNewComment] = useState('');

  // 模拟协作者数据
  useEffect(() => {
    const mockCollaborators: Collaborator[] = [
      {
        id: 'user_001',
        name: '张三',
        email: '<EMAIL>',
        role: 'owner',
        status: 'online',
        lastSeen: '刚刚',
        permissions: {
          canEdit: true,
          canComment: true,
          canShare: true,
          canExport: true
        }
      },
      {
        id: 'user_002',
        name: '李四',
        email: '<EMAIL>',
        role: 'editor',
        status: 'online',
        lastSeen: '2分钟前',
        permissions: {
          canEdit: true,
          canComment: true,
          canShare: false,
          canExport: true
        }
      },
      {
        id: 'user_003',
        name: '王五',
        email: '<EMAIL>',
        role: 'viewer',
        status: 'away',
        lastSeen: '1小时前',
        permissions: {
          canEdit: false,
          canComment: true,
          canShare: false,
          canExport: false
        }
      }
    ];

    const mockComments: Comment[] = [
      {
        id: 'comment_001',
        userId: 'user_002',
        userName: '李四',
        content: '这个技术方案需要补充更多的架构细节',
        timestamp: '2024-12-30 14:30',
        position: {
          section: '技术方案',
          paragraph: 2
        },
        resolved: false,
        replies: [
          {
            id: 'reply_001',
            userId: 'user_001',
            userName: '张三',
            content: '好的，我会在下个版本中补充',
            timestamp: '2024-12-30 14:35',
            resolved: false,
            replies: []
          }
        ]
      },
      {
        id: 'comment_002',
        userId: 'user_003',
        userName: '王五',
        content: '公司简介部分的案例可以更新一下',
        timestamp: '2024-12-30 13:45',
        position: {
          section: '公司简介',
          paragraph: 5
        },
        resolved: true,
        replies: []
      }
    ];

    setCollaborators(mockCollaborators);
    setComments(mockComments);
  }, []);

  const handleInviteUser = () => {
    if (!inviteEmail) {
      toast.error('请输入邮箱地址');
      return;
    }

    if (onInviteUser) {
      onInviteUser(inviteEmail, inviteRole);
    }

    // 模拟添加新协作者
    const newCollaborator: Collaborator = {
      id: `user_${Date.now()}`,
      name: inviteEmail.split('@')[0],
      email: inviteEmail,
      role: inviteRole,
      status: 'offline',
      lastSeen: '刚刚邀请',
      permissions: {
        canEdit: inviteRole === 'editor',
        canComment: true,
        canShare: false,
        canExport: inviteRole === 'editor'
      }
    };

    setCollaborators(prev => [...prev, newCollaborator]);
    setInviteEmail('');
    setShowInviteModal(false);
    toast.success(`已邀请 ${inviteEmail} 加入协作`);
  };

  const handleAddComment = () => {
    if (!newComment.trim()) {
      return;
    }

    const comment: Comment = {
      id: `comment_${Date.now()}`,
      userId: currentUser.id,
      userName: currentUser.name,
      content: newComment,
      timestamp: new Date().toLocaleString(),
      resolved: false,
      replies: []
    };

    setComments(prev => [comment, ...prev]);
    setNewComment('');
    toast.success('评论已添加');
  };

  const handleResolveComment = (commentId: string) => {
    setComments(prev => prev.map(comment => 
      comment.id === commentId 
        ? { ...comment, resolved: !comment.resolved }
        : comment
    ));
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown size={14} className="text-yellow-500" />;
      case 'editor':
        return <Edit3 size={14} className="text-blue-500" />;
      case 'viewer':
        return <Eye size={14} className="text-gray-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'owner':
        return '所有者';
      case 'editor':
        return '编辑者';
      case 'viewer':
        return '查看者';
      default:
        return role;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800">协作</h3>
          <Button
            size="sm"
            onClick={() => setShowInviteModal(true)}
          >
            <UserPlus size={16} className="mr-1" />
            邀请
          </Button>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200 mb-4">
          <button
            onClick={() => setActiveTab('collaborators')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'collaborators'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <Users size={16} className="inline mr-1" />
            协作者 ({collaborators.length})
          </button>
          <button
            onClick={() => setActiveTab('comments')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'comments'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            <MessageCircle size={16} className="inline mr-1" />
            评论 ({comments.filter(c => !c.resolved).length})
          </button>
        </div>

        {/* 协作者列表 */}
        {activeTab === 'collaborators' && (
          <div className="space-y-3">
            {collaborators.map((collaborator) => (
              <div key={collaborator.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm">
                      {collaborator.name.charAt(0)}
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(collaborator.status)}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {collaborator.name}
                      </p>
                      {getRoleIcon(collaborator.role)}
                    </div>
                    <p className="text-xs text-gray-500 truncate">{collaborator.email}</p>
                    <p className="text-xs text-gray-400">最后活动: {collaborator.lastSeen}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    {getRoleLabel(collaborator.role)}
                  </span>
                  {currentUser.role === 'owner' && collaborator.id !== currentUser.id && (
                    <button className="text-gray-400 hover:text-gray-600">
                      <Settings size={14} />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 评论列表 */}
        {activeTab === 'comments' && (
          <div className="space-y-4">
            {/* 添加评论 */}
            <div className="border border-gray-200 rounded-lg p-3">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="添加评论..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={3}
              />
              <div className="flex justify-end mt-2">
                <Button
                  size="sm"
                  onClick={handleAddComment}
                  disabled={!newComment.trim()}
                >
                  添加评论
                </Button>
              </div>
            </div>

            {/* 评论列表 */}
            <div className="space-y-3">
              {comments.map((comment) => (
                <div
                  key={comment.id}
                  className={`border rounded-lg p-3 ${
                    comment.resolved ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">
                        {comment.userName.charAt(0)}
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {comment.userName}
                      </span>
                      <span className="text-xs text-gray-500">
                        {comment.timestamp}
                      </span>
                      {comment.position && (
                        <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded">
                          {comment.position.section}
                        </span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => handleResolveComment(comment.id)}
                      className={`text-xs px-2 py-1 rounded ${
                        comment.resolved
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {comment.resolved ? '已解决' : '标记解决'}
                    </button>
                  </div>
                  
                  <p className={`text-sm mb-2 ${comment.resolved ? 'text-gray-500' : 'text-gray-700'}`}>
                    {comment.content}
                  </p>
                  
                  {/* 回复 */}
                  {comment.replies.length > 0 && (
                    <div className="ml-4 space-y-2 border-l-2 border-gray-200 pl-3">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="text-sm">
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-gray-900">{reply.userName}</span>
                            <span className="text-xs text-gray-500">{reply.timestamp}</span>
                          </div>
                          <p className="text-gray-700">{reply.content}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* 邀请用户模态框 */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="border-b px-6 py-4">
              <h3 className="text-lg font-medium text-gray-900">邀请协作者</h3>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱地址
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="输入邮箱地址"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  权限角色
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value as 'editor' | 'viewer')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="viewer">查看者 - 只能查看和评论</option>
                  <option value="editor">编辑者 - 可以编辑和评论</option>
                </select>
              </div>
            </div>
            
            <div className="border-t px-6 py-4 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowInviteModal(false)}
              >
                取消
              </Button>
              <Button onClick={handleInviteUser}>
                发送邀请
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaborationPanel;