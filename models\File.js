import mongoose from 'mongoose';

// 文件信息模型
const fileSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true,
    unique: true
  },
  type: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['bidding', 'certificates', 'cases', 'technical', 'other']
  },
  filePath: {
    type: String,
    required: true
  },
  uploadDate: {
    type: Date,
    default: Date.now
  },
  description: {
    type: String,
    default: ''
  },
  tags: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  metadata: {
    extractedText: String,
    analysisResult: mongoose.Schema.Types.Mixed,
    lastAnalyzed: Date
  }
}, {
  timestamps: true
});

// 索引
fileSchema.index({ category: 1, uploadDate: -1 });
fileSchema.index({ name: 'text', description: 'text' });

// 虚拟字段 - 文件URL
fileSchema.virtual('url').get(function() {
  return `/api/files/${this._id}/download`;
});

// 虚拟字段 - 预览URL
fileSchema.virtual('previewUrl').get(function() {
  return `/api/files/${this._id}/preview`;
});

// 实例方法 - 软删除
fileSchema.methods.softDelete = function() {
  this.isActive = false;
  return this.save();
};

// 静态方法 - 按类别查找
fileSchema.statics.findByCategory = function(category) {
  return this.find({ category, isActive: true }).sort({ uploadDate: -1 });
};

// 静态方法 - 搜索文件
fileSchema.statics.searchFiles = function(query) {
  return this.find({
    $and: [
      { isActive: true },
      {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { tags: { $in: [new RegExp(query, 'i')] } }
        ]
      }
    ]
  }).sort({ uploadDate: -1 });
};

export default mongoose.model('File', fileSchema);