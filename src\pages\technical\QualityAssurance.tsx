import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';
import AiChat from '../../components/ai/AiChat';

const QualityAssurance: React.FC = () => {
  const [showAiChat, setShowAiChat] = useState(false);

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">质量保证</h2>
            <p className="text-gray-500 mt-1">项目质量保证体系和措施</p>
          </div>
          <Button
            onClick={() => setShowAiChat(true)}
          >
            <Bot size={16} className="mr-2" />
            AI辅助编写
          </Button>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              质量管理体系
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请描述公司的质量管理体系和认证情况..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              质量保证措施
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请详细说明项目实施过程中的质量保证措施..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              质量控制方法
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请说明项目各阶段的质量控制方法..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              验收标准
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请列出项目验收的具体标准和要求..."
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline">保存草稿</Button>
          <Button>确认提交</Button>
        </div>
      </Card>

      {showAiChat && (
        <AiChat 
          onClose={() => setShowAiChat(false)} 
          context="质量保证编写" 
        />
      )}
    </div>
  );
};

export default QualityAssurance;