import React, { useState, useEffect } from 'react';
import { FileText, Search, Filter, Star, Download, Eye, Plus, Edit3 } from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';
import { toast } from 'react-toastify';

interface Template {
  id: string;
  name: string;
  description: string;
  category: 'technical' | 'commercial' | 'mixed';
  type: 'company-profile' | 'technical-solution' | 'implementation-plan' | 'pricing-strategy' | 'cost-breakdown' | 'contract-terms';
  content: string;
  tags: string[];
  rating: number;
  downloads: number;
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
  industry?: string;
  projectType?: string;
}

interface TemplateLibraryProps {
  onSelectTemplate?: (template: Template) => void;
  onCreateTemplate?: () => void;
  category?: 'technical' | 'commercial' | 'mixed';
}

const TemplateLibrary: React.FC<TemplateLibraryProps> = ({
  onSelectTemplate,
  onCreateTemplate,
  category
}) => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>(category || 'all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'rating' | 'downloads' | 'createdAt'>('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [isLoading, setIsLoading] = useState(true);
  const [showPreview, setShowPreview] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // 模拟模板数据
  const mockTemplates: Template[] = [
    {
      id: 'tpl_001',
      name: '软件开发公司简介模板',
      description: '适用于软件开发类项目的公司简介标准模板，包含资质展示、技术实力、成功案例等内容',
      category: 'technical',
      type: 'company-profile',
      content: `
# 公司简介

## 公司基本信息
[公司名称]成立于[成立年份]，注册资本[注册资本]万元，是一家专业从事软件开发、系统集成的高新技术企业。

## 资质证书
- 软件企业认定证书
- ISO9001质量管理体系认证
- CMMI3级认证
- 高新技术企业证书

## 技术实力
公司拥有一支经验丰富的技术团队，核心技术人员均具有[年限]年以上的项目经验...

## 成功案例
1. [项目名称] - [客户名称]
   项目规模：[规模描述]
   技术特点：[技术特点]
   项目成果：[成果描述]

2. [项目名称] - [客户名称]
   ...
      `,
      tags: ['软件开发', '公司简介', '技术标'],
      rating: 4.8,
      downloads: 156,
      createdAt: '2024-01-15',
      updatedAt: '2024-12-01',
      isDefault: true,
      industry: '软件开发',
      projectType: '系统开发'
    },
    {
      id: 'tpl_002',
      name: '技术解决方案模板',
      description: '通用技术解决方案模板，包含架构设计、技术选型、实现方案等完整内容',
      category: 'technical',
      type: 'technical-solution',
      content: `
# 技术解决方案

## 总体架构
### 系统架构设计
本项目采用[架构模式]架构，整体分为[层次]层：
- 表现层：[技术栈]
- 业务层：[技术栈]
- 数据层：[技术栈]

### 技术选型
- 前端技术：[前端技术栈]
- 后端技术：[后端技术栈]
- 数据库：[数据库选择]
- 中间件：[中间件选择]

## 功能设计
### 核心功能模块
1. [功能模块1]
   - 功能描述：[详细描述]
   - 技术实现：[实现方案]
   
2. [功能模块2]
   - 功能描述：[详细描述]
   - 技术实现：[实现方案]

## 性能保障
- 并发处理能力：[具体指标]
- 响应时间：[具体指标]
- 可用性：[具体指标]
      `,
      tags: ['技术方案', '架构设计', '技术标'],
      rating: 4.6,
      downloads: 89,
      createdAt: '2024-02-10',
      updatedAt: '2024-11-20',
      isDefault: true,
      industry: '通用',
      projectType: '系统开发'
    },
    {
      id: 'tpl_003',
      name: '项目实施计划模板',
      description: '详细的项目实施计划模板，包含团队组织、进度安排、风险管理等内容',
      category: 'technical',
      type: 'implementation-plan',
      content: `
# 项目实施计划

## 项目团队组织
### 组织架构
- 项目经理：[姓名及职责]
- 技术负责人：[姓名及职责]
- 开发团队：[人员配置]
- 测试团队：[人员配置]

### 人员配置
| 角色 | 人数 | 技能要求 | 工作内容 |
|------|------|----------|----------|
| 项目经理 | 1 | [技能要求] | [工作内容] |
| 架构师 | 1 | [技能要求] | [工作内容] |

## 实施进度
### 项目阶段划分
1. 需求分析阶段（[时间]）
   - 需求调研
   - 需求分析
   - 需求确认

2. 设计阶段（[时间]）
   - 概要设计
   - 详细设计
   - 设计评审

## 风险管理
### 风险识别
1. 技术风险
   - 风险描述：[描述]
   - 应对措施：[措施]

2. 进度风险
   - 风险描述：[描述]
   - 应对措施：[措施]
      `,
      tags: ['实施计划', '项目管理', '技术标'],
      rating: 4.7,
      downloads: 124,
      createdAt: '2024-01-20',
      updatedAt: '2024-12-05',
      isDefault: true,
      industry: '通用',
      projectType: '通用'
    },
    {
      id: 'tpl_004',
      name: '报价策略模板',
      description: '商务标报价策略模板，包含竞争分析、价格优势、成本控制等内容',
      category: 'commercial',
      type: 'pricing-strategy',
      content: `
# 报价策略

## 总体报价思路
本项目报价基于以下原则：
- 合理性原则：确保报价符合市场行情
- 竞争性原则：在保证质量的前提下具有价格优势
- 可持续性原则：保证项目的可持续发展

## 竞争分析
### 市场调研
根据市场调研，同类项目的价格区间为[价格区间]，我们的报价策略是：
- 总体报价：[报价金额]
- 市场定位：[定位描述]
- 竞争优势：[优势描述]

### 竞争对手分析
1. 竞争对手A
   - 预估报价：[金额]
   - 优势：[优势分析]
   - 劣势：[劣势分析]

## 价格优势
### 成本控制优势
- 技术成熟度高，开发效率提升[百分比]
- 团队经验丰富，减少返工成本
- 采用标准化开发流程，降低管理成本

### 性价比优势
- 提供超出招标要求的增值服务
- 长期技术支持和维护
- 免费培训和知识转移
      `,
      tags: ['报价策略', '商务标', '成本分析'],
      rating: 4.5,
      downloads: 78,
      createdAt: '2024-02-15',
      updatedAt: '2024-11-25',
      isDefault: true,
      industry: '通用',
      projectType: '通用'
    }
  ];

  useEffect(() => {
    // 模拟加载模板数据
    setIsLoading(true);
    setTimeout(() => {
      setTemplates(mockTemplates);
      setIsLoading(false);
    }, 1000);
  }, []);

  // 过滤和排序模板
  const filteredAndSortedTemplates = templates
    .filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      const matchesType = selectedType === 'all' || template.type === selectedType;
      const matchesIndustry = selectedIndustry === 'all' || template.industry === selectedIndustry;
      
      return matchesSearch && matchesCategory && matchesType && matchesIndustry;
    })
    .sort((a, b) => {
      let aVal, bVal;
      
      switch (sortBy) {
        case 'name':
          aVal = a.name;
          bVal = b.name;
          break;
        case 'rating':
          aVal = a.rating;
          bVal = b.rating;
          break;
        case 'downloads':
          aVal = a.downloads;
          bVal = b.downloads;
          break;
        case 'createdAt':
          aVal = new Date(a.createdAt).getTime();
          bVal = new Date(b.createdAt).getTime();
          break;
        default:
          return 0;
      }
      
      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortOrder === 'asc' 
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      } else {
        return sortOrder === 'asc' 
          ? (aVal as number) - (bVal as number)
          : (bVal as number) - (aVal as number);
      }
    });

  // 收藏功能
  const toggleFavorite = (templateId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(templateId)) {
        newFavorites.delete(templateId);
        toast.success('已从收藏中移除');
      } else {
        newFavorites.add(templateId);
        toast.success('已添加到收藏');
      }
      // 保存到localStorage
      localStorage.setItem('templateFavorites', JSON.stringify([...newFavorites]));
      return newFavorites;
    });
  };

  // 下载模板
  const downloadTemplate = (template: Template) => {
    const blob = new Blob([template.content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${template.name}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    // 增加下载次数（模拟）
    setTemplates(prev => prev.map(t => 
      t.id === template.id 
        ? { ...t, downloads: t.downloads + 1 }
        : t
    ));
    
    toast.success('模板下载成功');
  };

  // 复制模板内容
  const copyTemplate = async (template: Template) => {
    try {
      await navigator.clipboard.writeText(template.content);
      toast.success('模板内容已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败，请手动选择复制');
    }
  };

  // 从localStorage加载收藏
  useEffect(() => {
    const savedFavorites = localStorage.getItem('templateFavorites');
    if (savedFavorites) {
      setFavorites(new Set(JSON.parse(savedFavorites)));
    }
  }, []);

  const handleUseTemplate = (template: Template) => {
    if (onSelectTemplate) {
      onSelectTemplate(template);
      toast.success(`已应用模板：${template.name}`);
    }
  };

  const handlePreviewTemplate = (templateId: string) => {
    setShowPreview(templateId);
  };

  const categories = [
    { value: 'all', label: '全部' },
    { value: 'technical', label: '技术标' },
    { value: 'commercial', label: '商务标' },
    { value: 'mixed', label: '综合' }
  ];

  const types = [
    { value: 'all', label: '全部类型' },
    { value: 'company-profile', label: '公司简介' },
    { value: 'technical-solution', label: '技术方案' },
    { value: 'implementation-plan', label: '实施计划' },
    { value: 'pricing-strategy', label: '报价策略' },
    { value: 'cost-breakdown', label: '成本明细' },
    { value: 'contract-terms', label: '合同条款' }
  ];

  const industries = [
    { value: 'all', label: '全部行业' },
    { value: '通用', label: '通用' },
    { value: '软件开发', label: '软件开发' },
    { value: '系统集成', label: '系统集成' },
    { value: '建筑工程', label: '建筑工程' },
    { value: '设备采购', label: '设备采购' },
    { value: '咨询服务', label: '咨询服务' }
  ];

  const sortOptions = [
    { value: 'rating', label: '按评分' },
    { value: 'downloads', label: '按下载量' },
    { value: 'createdAt', label: '按创建时间' },
    { value: 'name', label: '按名称' }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-800">模板库</h2>
            <p className="text-gray-500">选择合适的模板快速开始标书制作</p>
          </div>
          {onCreateTemplate && (
            <Button onClick={onCreateTemplate}>
              <Plus size={16} className="mr-2" />
              创建模板
            </Button>
          )}
        </div>

        {/* 搜索和筛选 */}
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="搜索模板..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex flex-col sm:flex-row gap-2">
            {/* 排序选择 */}
            <div className="flex items-center space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                title={sortOrder === 'asc' ? '升序' : '降序'}
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>
        </div>

        {/* 高级筛选 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {categories.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {types.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>

          <select
            value={selectedIndustry}
            onChange={(e) => setSelectedIndustry(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {industries.map(industry => (
              <option key={industry.value} value={industry.value}>
                {industry.label}
              </option>
            ))}
          </select>

          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
              setSelectedType('all');
              setSelectedIndustry('all');
              setSortBy('rating');
              setSortOrder('desc');
            }}
          >
            重置筛选
          </Button>
        </div>

        {/* 统计信息 */}
        <div className="mb-4 text-sm text-gray-600">
          共找到 {filteredAndSortedTemplates.length} 个模板
          {favorites.size > 0 && ` • ${favorites.size} 个收藏`}
        </div>

        {/* 模板列表 */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-gray-500">加载模板中...</p>
            </div>
          </div>
        ) : filteredAndSortedTemplates.length === 0 ? (
          <div className="text-center py-12">
            <FileText size={48} className="text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">没有找到匹配的模板</p>
            <Button variant="outline" onClick={() => {
              setSearchTerm('');
              setSelectedCategory('all');
              setSelectedType('all');
              setSelectedIndustry('all');
            }}>
              清除筛选条件
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredAndSortedTemplates.map((template) => (
              <div key={template.id} className="bg-white border border-gray-200 rounded-lg p-5 hover:shadow-lg transition-all duration-200 relative">
                {/* 收藏按钮 */}
                <button
                  onClick={() => toggleFavorite(template.id)}
                  className={`absolute top-3 right-3 p-1 rounded-full transition-colors ${
                    favorites.has(template.id) 
                      ? 'text-red-500 hover:text-red-600' 
                      : 'text-gray-300 hover:text-red-400'
                  }`}
                >
                  <Star size={18} fill={favorites.has(template.id) ? 'currentColor' : 'none'} />
                </button>

                <div className="flex items-start mb-4">
                  <div className="p-2 bg-blue-50 rounded-lg mr-3">
                    <FileText size={20} className="text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 text-base mb-1 pr-8">{template.name}</h3>
                    <div className="flex items-center gap-2 mb-2">
                      {template.isDefault && (
                        <span className="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                          官方模板
                        </span>
                      )}
                      <span className="text-xs text-gray-500">{template.industry}</span>
                      <div className="flex items-center text-yellow-500">
                        <Star size={14} fill="currentColor" />
                        <span className="text-xs text-gray-600 ml-1">{template.rating}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-4 line-clamp-3 leading-relaxed">{template.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {template.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md">
                      #{tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="text-xs text-gray-400">+{template.tags.length - 3}</span>
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4 pt-2 border-t border-gray-100">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <Download size={12} className="mr-1" />
                      {template.downloads}
                    </span>
                    <span>更新于 {template.updatedAt}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowPreview(template.id)}
                    className="flex items-center justify-center"
                  >
                    <Eye size={14} className="mr-1" />
                    预览
                  </Button>
                  <div className="relative">
                    <Button
                      size="sm"
                      onClick={() => handleUseTemplate(template)}
                      className="w-full flex items-center justify-center"
                    >
                      使用模板
                    </Button>
                    <div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            downloadTemplate(template);
                          }}
                          className="p-1 bg-green-100 hover:bg-green-200 text-green-600 rounded-full text-xs"
                          title="下载模板"
                        >
                          <Download size={12} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            copyTemplate(template);
                          }}
                          className="p-1 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-full text-xs ml-1"
                          title="复制内容"
                        >
                          <Edit3 size={12} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {filteredAndSortedTemplates.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的模板</h3>
            <p className="text-gray-500 mb-4">请尝试调整搜索条件或筛选选项</p>
            {onCreateTemplate && (
              <Button onClick={onCreateTemplate}>
                <Plus size={16} className="mr-2" />
                创建新模板
              </Button>
            )}
          </div>
        )}
      </Card>

      {/* 模板预览模态框 */}
      {showPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="border-b px-6 py-4 flex items-center justify-between">
              <h3 className="text-lg font-medium text-gray-900">模板预览</h3>
              <button
                onClick={() => setShowPreview(null)}
                className="text-gray-400 hover:text-gray-500"
              >
                ×
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-6">
              {(() => {
                const template = templates.find(t => t.id === showPreview);
                return template ? (
                  <div className="prose max-w-none">
                    <pre className="whitespace-pre-wrap text-sm">{template.content}</pre>
                  </div>
                ) : null;
              })()}
            </div>
            
            <div className="border-t px-6 py-4 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPreview(null)}
              >
                关闭
              </Button>
              <Button
                onClick={() => {
                  const template = templates.find(t => t.id === showPreview);
                  if (template) {
                    handleUseTemplate(template);
                    setShowPreview(null);
                  }
                }}
              >
                使用此模板
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateLibrary;