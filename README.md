# 项目投标标书制作平台

## 项目结构

### 前端 (React + TypeScript + Vite)
```
src/
├── components/           # 组件目录
│   ├── ai/              # AI相关组件
│   │   └── AiChat.tsx   # AI聊天组件
│   ├── bidding/         # 标书相关组件
│   │   ├── TechnicalBidNav.tsx    # 技术标导航
│   │   └── CommercialBidNav.tsx   # 商务标导航
│   ├── common/          # 通用组件
│   │   └── Logo.tsx     # Logo组件
│   ├── files/           # 文件管理组件
│   │   └── UploadModal.tsx        # 文件上传模态框
│   ├── navigation/      # 导航组件
│   │   ├── Header.tsx   # 头部导航
│   │   └── Sidebar.tsx  # 侧边栏
│   └── ui/              # UI基础组件
│       ├── Button.tsx   # 按钮组件
│       └── Card.tsx     # 卡片组件
├── layouts/             # 布局组件
│   └── MainLayout.tsx   # 主布局
├── pages/               # 页面组件
│   ├── technical/       # 技术标页面
│   │   ├── Overview.tsx
│   │   ├── CompanyProfile.tsx
│   │   ├── ProjectUnderstanding.tsx
│   │   ├── TechnicalSolution.tsx
│   │   ├── ImplementationPlan.tsx
│   │   └── QualityAssurance.tsx
│   ├── commercial/      # 商务标页面
│   │   ├── Overview.tsx
│   │   ├── PricingStrategy.tsx
│   │   ├── CostBreakdown.tsx
│   │   ├── PaymentTerms.tsx
│   │   └── ContractConditions.tsx
│   ├── Dashboard.tsx    # 首页
│   ├── TechnicalBid.tsx # 技术标主页
│   ├── CommercialBid.tsx # 商务标主页
│   ├── FileManagement.tsx # 文件管理
│   ├── Settings.tsx     # 系统设置
│   ├── Copyright.tsx    # 版权信息
│   └── NotFound.tsx     # 404页面
├── services/            # 服务层
│   ├── api.ts          # API接口服务
│   ├── ai.ts           # AI服务
│   └── database.ts     # 数据库服务
├── App.tsx             # 应用主组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

### 后端 (Node.js + Express)
```
根目录/
├── server.js           # Express服务器主文件
├── data/               # 数据存储目录
│   └── files.json      # 文件信息存储
├── uploads/            # 文件上传目录
└── package.json        # 项目配置
```

## API接口规范

### 基础配置

#### 开发环境端口
- 前端开发服务器: 5173 (Vite)
- 后端API服务器: 3008 (Express)
- API代理: `/api` -> `http://localhost:3008/api`

#### 生产环境端口 (Docker)
- 应用容器内部: 3008 (Express)
- 应用外部访问: 3010 (映射到容器3008)
- Nginx反向代理: 8080 (映射到容器80)
- 前端构建产物: 由Express在3008端口提供静态文件服务

### 文件管理API

#### 1. 上传文件
```
POST /api/files/upload
Content-Type: multipart/form-data

Body:
- files: File[] (文件数组)
- category: string (文件类别)

Response:
{
  "message": "文件上传成功",
  "files": [
    {
      "id": "文件ID",
      "filename": "存储文件名",
      "originalname": "原始文件名",
      "size": 文件大小,
      "category": "文件类别"
    }
  ]
}
```

#### 2. 获取文件列表
```
GET /api/files?category=文件类别

Response:
[
  {
    "id": "文件ID",
    "name": "文件名",
    "type": "文件类型",
    "size": 文件大小,
    "category": "文件类别",
    "uploadDate": "上传日期",
    "filePath": "文件路径",
    "filename": "存储文件名"
  }
]
```

#### 3. 删除文件
```
DELETE /api/files/:fileId

Response:
{
  "message": "文件删除成功"
}
```

#### 4. 下载文件
```
GET /api/files/:fileId/download

Response: 文件流
```

#### 5. 预览文件
```
GET /api/files/:fileId/preview

Response: 文件流 (支持PDF、图片、文本文件)
```

## 数据库配置

### MongoDB
- 连接地址: mongodb://localhost:27017/bidding_platform
- 用途: 存储项目数据、用户数据、文件元数据

### Neo4j
- 连接地址: neo4j://localhost:7687
- 用户名: neo4j
- 密码: password123
- 用途: 存储知识图谱、关系数据

### PostgreSQL
- 连接地址: postgresql://localhost:5432/bidding_platform
- 用途: 存储结构化业务数据

## 启动说明

### 开发环境启动
1. 启动后端服务器:
   ```bash
   npm run server
   ```

2. 启动前端开发服务器:
   ```bash
   npm run dev
   ```

### 生产环境部署
1. 构建前端:
   ```bash
   npm run build
   ```

2. 启动生产服务器:
   ```bash
   npm run server
   ```

## 技术栈

### 前端
- React 18
- TypeScript
- Vite
- Tailwind CSS
- React Router
- Framer Motion
- Lucide React
- Axios
- React Toastify

### 后端
- Node.js
- Express
- Multer (文件上传)
- CORS
- MongoDB (Mongoose)
- Neo4j
- PostgreSQL

### AI集成
- DeepSeek API (默认)
- 阿里云通义千问
- 腾讯云文生图

## 安全配置

### 文件上传限制
- 单文件最大: 100MB
- 最多文件数: 10个
- 支持格式: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, JPG, JPEG, PNG, GIF, ZIP, RAR

### CORS配置
- 允许所有来源 (开发环境)
- 支持凭据传递

## 环境变量
```
# MongoDB
MONGODB_URI=mongodb://localhost:27017/bidding_platform

# Neo4j
NEO4J_URI=neo4j://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password123

# PostgreSQL
POSTGRES_URI=postgresql://localhost:5432/bidding_platform

# AI API Keys
DEEPSEEK_API_KEY=***********************************
ALIBABA_API_KEY=sk-28ec47f5eff6404aa98329a9f56ada05
TENCENT_API_KEY=sk-tsfTXZUNgkePyo5SBWBaLI0ayxA6USvnkoVxZTSgGrQpxaJ6

# 服务器配置
PORT=3000
UPLOAD_DIR=./uploads
DATA_DIR=./data
```