import React, { useState } from 'react';
import { 
  Settings as SettingsIcon, Server, Database, Bot, Shield, 
  Activity, UserCog, Info, CheckCircle, AlertTriangle, XCircle
} from 'lucide-react';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { toast } from 'react-toastify';

const Settings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('system');
  
  const tabs = [
    { id: 'system', label: '系统设置', icon: <SettingsIcon size={18} /> },
    { id: 'api', label: 'API设置', icon: <Server size={18} /> },
    { id: 'database', label: '数据库设置', icon: <Database size={18} /> },
    { id: 'ai', label: 'AI模型设置', icon: <Bot size={18} /> },
    { id: 'security', label: '安全设置', icon: <Shield size={18} /> },
    { id: 'monitoring', label: '系统监控', icon: <Activity size={18} /> },
    { id: 'account', label: '账户设置', icon: <UserCog size={18} /> },
    { id: 'about', label: '关于系统', icon: <Info size={18} /> },
  ];
  
  // 模拟系统健康状态
  const systemStatus = {
    api: { status: 'good', message: 'API服务正常运行' },
    database: { status: 'warning', message: 'MongoDB连接延迟较高' },
    ai: { status: 'good', message: 'AI服务正常' },
    storage: { status: 'error', message: '存储空间不足，请及时清理' }
  };
  
  // 模拟API配置
  const [apiConfigs, setApiConfigs] = useState({
    deepseek: { 
      url: 'https://api.deepseek.com/v1', 
      key: '***********************************',
      isDefault: true
    },
    alibaba: { 
      url: 'https://bailian.aliyuncs.com/v1/qwen/generation', 
      key: 'sk-28ec47f5eff6404aa98329a9f56ada05',
      isDefault: false
    },
    tencent: { 
      url: 'https://ai.tencentcloudapi.com', 
      key: 'sk-tsfTXZUNgkePyo5SBWBaLI0ayxA6USvnkoVxZTSgGrQpxaJ6',
      isDefault: false
    }
  });
  
  // 模拟数据库配置
  const [dbConfigs, setDbConfigs] = useState({
    mongodb: { 
      url: 'mongodb://localhost:27017/bidding_platform', 
      status: 'connected'
    },
    neo4j: { 
      url: 'neo4j://localhost:7687', 
      username: 'neo4j',
      password: '********',
      status: 'connected'
    }
  });
  
  const handleApiTest = (provider: string) => {
    toast.info(`正在测试 ${provider} API 连接...`);
    
    // 模拟API测试
    setTimeout(() => {
      if (provider === 'alibaba') {
        toast.warning('API连接成功，但响应时间较长');
      } else {
        toast.success('API连接测试成功');
      }
    }, 2000);
  };
  
  const handleDatabaseTest = (db: string) => {
    toast.info(`正在测试 ${db} 数据库连接...`);
    
    // 模拟数据库测试
    setTimeout(() => {
      if (db === 'mongodb') {
        toast.warning('数据库连接成功，但响应时间较长');
      } else {
        toast.success('数据库连接测试成功');
      }
    }, 2000);
  };
  
  const handleSetDefault = (provider: string) => {
    const newConfigs = {...apiConfigs};
    
    // 重置所有为非默认
    Object.keys(newConfigs).forEach(key => {
      newConfigs[key as keyof typeof apiConfigs].isDefault = false;
    });
    
    // 设置新的默认
    newConfigs[provider as keyof typeof apiConfigs].isDefault = true;
    
    setApiConfigs(newConfigs);
    toast.success(`已将 ${provider} 设为默认 API 提供商`);
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return <CheckCircle size={18} className="text-green-500" />;
      case 'warning':
        return <AlertTriangle size={18} className="text-yellow-500" />;
      case 'error':
        return <XCircle size={18} className="text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
        <p className="text-gray-500">管理系统配置和参数设置</p>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        {/* 左侧导航 */}
        <div className="w-full md:w-64 shrink-0">
          <Card className="p-0 overflow-hidden">
            <div className="bg-gray-50 px-4 py-3 border-b">
              <h3 className="text-sm font-medium text-gray-700">设置菜单</h3>
            </div>
            <nav className="p-2">
              <ul className="space-y-1">
                {tabs.map(tab => (
                  <li key={tab.id}>
                    <button
                      className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                        activeTab === tab.id
                          ? 'bg-blue-50 text-blue-700 font-medium'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <span className="mr-3">{tab.icon}</span>
                      <span>{tab.label}</span>
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </Card>
        </div>
        
        {/* 右侧内容 */}
        <div className="flex-1">
          {/* 系统设置 */}
          {activeTab === 'system' && (
            <Card>
              <h2 className="text-xl font-semibold text-gray-800 mb-6">系统设置</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-700 mb-4">系统健康状态</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between py-2 px-4 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Server size={18} className="text-gray-500 mr-3" />
                        <span className="text-gray-700">API服务</span>
                      </div>
                      <div className="flex items-center">
                        {getStatusIcon(systemStatus.api.status)}
                        <span className="ml-2 text-sm text-gray-600">{systemStatus.api.message}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between py-2 px-4 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Database size={18} className="text-gray-500 mr-3" />
                        <span className="text-gray-700">数据库</span>
                      </div>
                      <div className="flex items-center">
                        {getStatusIcon(systemStatus.database.status)}
                        <span className="ml-2 text-sm text-gray-600">{systemStatus.database.message}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between py-2 px-4 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Bot size={18} className="text-gray-500 mr-3" />
                        <span className="text-gray-700">AI服务</span>
                      </div>
                      <div className="flex items-center">
                        {getStatusIcon(systemStatus.ai.status)}
                        <span className="ml-2 text-sm text-gray-600">{systemStatus.ai.message}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between py-2 px-4 bg-gray-50 rounded-md">
                      <div className="flex items-center">
                        <Database size={18} className="text-gray-500 mr-3" />
                        <span className="text-gray-700">存储空间</span>
                      </div>
                      <div className="flex items-center">
                        {getStatusIcon(systemStatus.storage.status)}
                        <span className="ml-2 text-sm text-gray-600">{systemStatus.storage.message}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-700 mb-4">通用设置</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">系统名称</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        defaultValue="标书制作平台"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">系统Logo</label>
                      <div className="flex items-center">
                        <div className="flex items-center justify-center w-12 h-12 rounded-md bg-blue-600 text-white">
                          <FileText size={24} />
                        </div>
                        <Button variant="outline" size="sm" className="ml-4">
                          更改Logo
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">文件存储路径</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        defaultValue="/data/files"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">单文件大小限制</label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="50">50 MB</option>
                        <option value="100" selected>100 MB</option>
                        <option value="200">200 MB</option>
                        <option value="500">500 MB</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <Button variant="outline">取消</Button>
                <Button>保存设置</Button>
              </div>
            </Card>
          )}
          
          {/* API设置 */}
          {activeTab === 'api' && (
            <Card>
              <h2 className="text-xl font-semibold text-gray-800 mb-6">API设置</h2>
              
              <div className="space-y-6">
                {/* DeepSeek API */}
                <div className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-700">DeepSeek API</h3>
                    <div className="flex items-center">
                      {apiConfigs.deepseek.isDefault && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-2">默认</span>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleApiTest('deepseek')}
                      >
                        测试连接
                      </Button>
                      {!apiConfigs.deepseek.isDefault && (
                        <Button 
                          size="sm" 
                          className="ml-2"
                          onClick={() => handleSetDefault('deepseek')}
                        >
                          设为默认
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.deepseek.url}
                        readOnly
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                      <input 
                        type="password" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.deepseek.key}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
                
                {/* 阿里云 API */}
                <div className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-700">阿里云 API</h3>
                    <div className="flex items-center">
                      {apiConfigs.alibaba.isDefault && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-2">默认</span>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleApiTest('alibaba')}
                      >
                        测试连接
                      </Button>
                      {!apiConfigs.alibaba.isDefault && (
                        <Button 
                          size="sm" 
                          className="ml-2"
                          onClick={() => handleSetDefault('alibaba')}
                        >
                          设为默认
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.alibaba.url}
                        readOnly
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                      <input 
                        type="password" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.alibaba.key}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
                
                {/* 腾讯云 API */}
                <div className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-700">腾讯云 API (文生图)</h3>
                    <div className="flex items-center">
                      {apiConfigs.tencent.isDefault && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-2">默认</span>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleApiTest('tencent')}
                      >
                        测试连接
                      </Button>
                      {!apiConfigs.tencent.isDefault && (
                        <Button 
                          size="sm" 
                          className="ml-2"
                          onClick={() => handleSetDefault('tencent')}
                        >
                          设为默认
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.tencent.url}
                        readOnly
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                      <input 
                        type="password" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={apiConfigs.tencent.key}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <Button variant="outline">取消</Button>
                <Button>保存设置</Button>
              </div>
            </Card>
          )}
          
          {/* 数据库设置 */}
          {activeTab === 'database' && (
            <Card>
              <h2 className="text-xl font-semibold text-gray-800 mb-6">数据库设置</h2>
              
              <div className="space-y-6">
                {/* MongoDB */}
                <div className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-700">MongoDB</h3>
                    <div className="flex items-center">
                      <span className={`px-2 py-1 text-xs rounded-full mr-2 ${
                        dbConfigs.mongodb.status === 'connected' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {dbConfigs.mongodb.status === 'connected' ? '已连接' : '未连接'}
                      </span>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDatabaseTest('mongodb')}
                      >
                        测试连接
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">连接 URL</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={dbConfigs.mongodb.url}
                        readOnly
                      />
                    </div>
                  </div>
                </div>
                
                {/* Neo4j */}
                <div className="border rounded-md p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-700">Neo4j</h3>
                    <div className="flex items-center">
                      <span className={`px-2 py-1 text-xs rounded-full mr-2 ${
                        dbConfigs.neo4j.status === 'connected' 
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {dbConfigs.neo4j.status === 'connected' ? '已连接' : '未连接'}
                      </span>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleDatabaseTest('neo4j')}
                      >
                        测试连接
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">连接 URL</label>
                      <input 
                        type="text" 
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={dbConfigs.neo4j.url}
                        readOnly
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input 
                          type="text" 
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          value={dbConfigs.neo4j.username}
                          readOnly
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">密码</label>
                        <input 
                          type="password" 
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          value={dbConfigs.neo4j.password}
                          readOnly
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end space-x-3">
                <Button variant="outline">取消</Button>
                <Button>保存设置</Button>
              </div>
            </Card>
          )}
          
          {/* 其他标签页内容... */}
          {activeTab !== 'system' && activeTab !== 'api' && activeTab !== 'database' && (
            <Card className="flex items-center justify-center p-12">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 rounded-full bg-blue-100">
                    <Settings className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">
                  {tabs.find(tab => tab.id === activeTab)?.label} 功能正在开发中
                </h3>
                <p className="text-gray-500 max-w-md">
                  此功能将在后续版本中推出，敬请期待。如有任何建议，请联系我们的开发团队。
                </p>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;