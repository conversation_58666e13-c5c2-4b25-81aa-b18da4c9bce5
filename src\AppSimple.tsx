import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import FileManagement from './pages/FileManagement';

function AppSimple() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <ToastContainer position="top-right" autoClose={3000} />
        
        {/* 简化的导航栏 */}
        <nav className="bg-white shadow-sm border-b px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">AI投标文件制作平台</h1>
            <div className="flex space-x-4">
              <Link to="/" className="text-blue-600 hover:text-blue-800">首页</Link>
              <Link to="/files" className="text-blue-600 hover:text-blue-800">文件管理</Link>
            </div>
          </div>
        </nav>

        {/* 主内容区域 */}
        <main className="container mx-auto p-6">
          <Routes>
            <Route path="/" element={
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold mb-4">欢迎使用AI投标文件制作平台</h2>
                <p className="text-gray-600 mb-8">智能化的投标文件制作解决方案</p>
                <Link 
                  to="/files" 
                  className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  开始文件管理
                </Link>
              </div>
            } />
            <Route path="/files" element={<FileManagement />} />
            <Route path="*" element={
              <div className="text-center py-12">
                <h2 className="text-2xl font-bold mb-4">页面未找到</h2>
                <Link to="/" className="text-blue-600 hover:text-blue-800">返回首页</Link>
              </div>
            } />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default AppSimple;