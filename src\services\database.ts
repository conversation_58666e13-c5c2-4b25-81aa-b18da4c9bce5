import mongoose from 'mongoose';
import neo4j from 'neo4j-driver';

// MongoDB连接
export const connectMongoDB = async () => {
  try {
    // 这里使用模拟的连接字符串，实际项目中应从环境变量或配置文件获取
    const mongoURI = 'mongodb://localhost:27017/bidding_platform';
    
    await mongoose.connect(mongoURI);
    console.log('MongoDB连接成功');
    
    return true;
  } catch (error) {
    console.error('MongoDB连接失败:', error);
    return false;
  }
};

// Neo4j连接
export const connectNeo4j = async () => {
  try {
    // 这里使用模拟的连接信息，实际项目中应从环境变量或配置文件获取
    const uri = 'neo4j://localhost:7687';
    const user = 'neo4j';
    const password = 'password';
    
    const driver = neo4j.driver(uri, neo4j.auth.basic(user, password));
    
    // 测试连接
    const session = driver.session();
    await session.run('RETURN 1');
    await session.close();
    
    console.log('Neo4j连接成功');
    
    return { driver, status: true };
  } catch (error) {
    console.error('Neo4j连接失败:', error);
    return { driver: null, status: false };
  }
};

// MongoDB模型定义
// 文件模型
export const FileSchema = new mongoose.Schema({
  name: { type: String, required: true },
  originalName: { type: String, required: true },
  type: { type: String, required: true },
  size: { type: Number, required: true },
  path: { type: String, required: true },
  category: { 
    type: String, 
    enum: ['招标方文件', '投标方文件', '供应商文件'],
    required: true 
  },
  uploadDate: { type: Date, default: Date.now },
  metadata: { type: mongoose.Schema.Types.Mixed, default: {} },
});

// 项目模型
export const ProjectSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String },
  client: { type: String, required: true },
  deadline: { type: Date, required: true },
  status: { 
    type: String, 
    enum: ['草稿', '进行中', '已完成', '已取消'],
    default: '草稿'
  },
  technicalBid: {
    companyProfile: { type: String },
    projectUnderstanding: { type: String },
    technicalSolution: { type: String },
    implementationPlan: { type: String },
    qualityAssurance: { type: String },
  },
  commercialBid: {
    pricingStrategy: { type: String },
    costBreakdown: { type: String },
    paymentTerms: { type: String },
    contractConditions: { type: String },
  },
  files: [{ type: mongoose.Schema.Types.ObjectId, ref: 'File' }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// 动态创建模型
export const createModels = () => {
  const File = mongoose.models.File || mongoose.model('File', FileSchema);
  const Project = mongoose.models.Project || mongoose.model('Project', ProjectSchema);
  
  return { File, Project };
};

export default {
  connectMongoDB,
  connectNeo4j,
  createModels,
};