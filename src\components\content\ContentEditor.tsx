import React, { useState, useRef } from 'react';
import { Save, Bot, Eye, RotateCcw, CheckCircle, AlertTriangle } from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import EnhancedAiChat from '../ai/EnhancedAiChat';

interface ContentEditorProps {
  title: string;
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  documentData?: any;
  requirements?: any[];
  onSave?: () => void;
}

const ContentEditor: React.FC<ContentEditorProps> = ({
  title,
  content,
  onChange,
  placeholder,
  documentData,
  requirements = [],
  onSave
}) => {
  const [showAiChat, setShowAiChat] = useState(false);
  const [isPreview, setIsPreview] = useState(false);
  const [savedContent, setSavedContent] = useState(content);
  const [complianceStatus, setComplianceStatus] = useState<{
    score: number;
    issues: any[];
    lastCheck: Date;
  } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSave = () => {
    setSavedContent(content);
    if (onSave) {
      onSave();
    }
  };

  const handleRevert = () => {
    onChange(savedContent);
  };

  const handleAiGenerated = (generatedContent: string) => {
    onChange(generatedContent);
  };

  const hasUnsavedChanges = content !== savedContent;

  const getComplianceColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getComplianceIcon = (score: number) => {
    if (score >= 80) return <CheckCircle size={16} className="text-green-600" />;
    if (score >= 60) return <AlertTriangle size={16} className="text-yellow-600" />;
    return <AlertTriangle size={16} className="text-red-600" />;
  };

  return (
    <div className="space-y-4">
      <Card>
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-800">{title}</h3>
            {complianceStatus && (
              <div className="flex items-center mt-1 text-sm">
                {getComplianceIcon(complianceStatus.score)}
                <span className={`ml-1 ${getComplianceColor(complianceStatus.score)}`}>
                  合规评分: {complianceStatus.score}/100
                </span>
                <span className="ml-2 text-gray-500">
                  (检查时间: {complianceStatus.lastCheck.toLocaleTimeString()})
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPreview(!isPreview)}
            >
              <Eye size={16} className="mr-1" />
              {isPreview ? '编辑' : '预览'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAiChat(true)}
            >
              <Bot size={16} className="mr-1" />
              AI助手
            </Button>
            
            {hasUnsavedChanges && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRevert}
              >
                <RotateCcw size={16} className="mr-1" />
                撤销
              </Button>
            )}
            
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasUnsavedChanges}
            >
              <Save size={16} className="mr-1" />
              保存
            </Button>
          </div>
        </div>

        {isPreview ? (
          <div className="min-h-[400px] p-4 border border-gray-200 rounded-md bg-gray-50">
            <div className="prose max-w-none">
              {content ? (
                <div className="whitespace-pre-wrap">{content}</div>
              ) : (
                <div className="text-gray-500 italic">暂无内容</div>
              )}
            </div>
          </div>
        ) : (
          <div className="relative">
            <textarea
              ref={textareaRef}
              value={content}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder || `请输入${title}内容...`}
              className="w-full h-96 px-4 py-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            />
            
            {/* 字数统计 */}
            <div className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white px-2 py-1 rounded">
              {content.length} 字符
            </div>
          </div>
        )}

        {/* 合规检查结果 */}
        {complianceStatus && complianceStatus.issues.length > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <h4 className="text-sm font-medium text-yellow-800 mb-2">需要注意的问题：</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              {complianceStatus.issues.slice(0, 3).map((issue, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>{issue.issue}</span>
                </li>
              ))}
              {complianceStatus.issues.length > 3 && (
                <li className="text-yellow-600">
                  ...还有 {complianceStatus.issues.length - 3} 个问题
                </li>
              )}
            </ul>
          </div>
        )}

        {/* 要求响应提示 */}
        {requirements.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 mb-2">需要响应的要求：</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              {requirements.slice(0, 3).map((req, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  <span className="truncate">{req.content}</span>
                </li>
              ))}
              {requirements.length > 3 && (
                <li className="text-blue-600">
                  ...还有 {requirements.length - 3} 项要求
                </li>
              )}
            </ul>
          </div>
        )}
      </Card>

      {showAiChat && (
        <EnhancedAiChat
          onClose={() => setShowAiChat(false)}
          context={title}
          documentData={documentData}
          currentContent={content}
          onContentGenerated={handleAiGenerated}
        />
      )}
    </div>
  );
};

export default ContentEditor;