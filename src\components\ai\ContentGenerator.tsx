import React, { useState } from 'react';
import { Wand2, Copy, Download, RefreshCw, Loader2, Check, FileText } from 'lucide-react';
import Button from '../ui/Button';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-toastify';
import ReactMarkdown from 'react-markdown';

interface ContentGeneratorProps {
  section: string;
  sectionTitle: string;
  context: string;
  prompts?: string[];
  onContentGenerated?: (content: string) => void;
}

interface GeneratedContent {
  id: string;
  content: string;
  timestamp: Date;
  prompt: string;
}

const ContentGenerator: React.FC<ContentGeneratorProps> = ({
  section,
  sectionTitle,
  context,
  prompts = [],
  onContentGenerated
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [selectedContent, setSelectedContent] = useState<string>('');
  const [showHistory, setShowHistory] = useState(false);

  // 默认提示词
  const defaultPrompts = [
    `请为${sectionTitle}部分生成专业的内容框架`,
    `详细阐述${sectionTitle}的实施方案`,
    `分析${sectionTitle}的关键要点和注意事项`,
    `提供${sectionTitle}的最佳实践建议`,
    ...prompts
  ];

  const generateContent = async (prompt: string) => {
    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: `${prompt}

请基于${context}的背景，为${sectionTitle}部分生成专业、详细、可操作的内容。内容应该：
1. 结构清晰，逻辑严密
2. 语言规范，符合招投标文档要求
3. 内容详实，具有可执行性
4. 突出技术优势和创新点
5. 符合行业标准和最佳实践

请使用Markdown格式输出，包含适当的标题、列表和表格。`,
          context,
          section
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success && data.content) {
        const newContent: GeneratedContent = {
          id: Date.now().toString(),
          content: data.content,
          timestamp: new Date(),
          prompt
        };

        setGeneratedContent(prev => [newContent, ...prev]);
        setSelectedContent(data.content);
        onContentGenerated?.(data.content);
        
        toast.success('内容生成成功！');
      } else {
        throw new Error(data.message || '内容生成失败');
      }
    } catch (error) {
      console.error('内容生成错误:', error);
      
      let errorMessage = '内容生成失败，请稍后重试';
      if (error instanceof Error) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接异常，请检查网络后重试';
        } else if (error.message.includes('HTTP 5')) {
          errorMessage = 'AI服务暂时不可用，请稍后重试';
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCustomGenerate = () => {
    if (!customPrompt.trim()) {
      toast.warning('请输入生成提示词');
      return;
    }
    generateContent(customPrompt.trim());
    setCustomPrompt('');
  };

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success('内容已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败，请手动选择复制');
    }
  };

  const downloadContent = (content: string, filename?: string) => {
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `${sectionTitle}_${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast.success('内容已下载');
  };

  const regenerateContent = (content: GeneratedContent) => {
    generateContent(content.prompt);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Wand2 className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">AI内容生成</h3>
            <p className="text-sm text-gray-500">{sectionTitle}</p>
          </div>
        </div>
        
        {generatedContent.length > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
          >
            <FileText className="w-4 h-4 mr-2" />
            历史记录 ({generatedContent.length})
          </Button>
        )}
      </div>

      {/* 快速生成按钮 */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          快速生成
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          {defaultPrompts.slice(0, 4).map((prompt, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => generateContent(prompt)}
              disabled={isGenerating}
              className="text-left justify-start h-auto py-2 px-3"
            >
              <div className="truncate text-xs">{prompt}</div>
            </Button>
          ))}
        </div>
      </div>

      {/* 自定义提示词 */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          自定义提示词
        </label>
        <div className="flex space-x-2">
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder={`请详细描述您需要生成的${sectionTitle}内容...`}
            className="flex-1 min-h-[80px] px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
            disabled={isGenerating}
          />
          <Button
            onClick={handleCustomGenerate}
            disabled={isGenerating || !customPrompt.trim()}
            className="self-end"
          >
            {isGenerating ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Wand2 className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* 生成状态 */}
      {isGenerating && (
        <div className="mb-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-5 h-5 text-purple-600 animate-spin" />
            <div>
              <p className="text-sm font-medium text-purple-900">AI正在生成内容...</p>
              <p className="text-xs text-purple-600">请稍等，这可能需要几秒钟</p>
            </div>
          </div>
        </div>
      )}

      {/* 生成的内容 */}
      {selectedContent && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-md font-medium text-gray-900">生成内容</h4>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(selectedContent)}
              >
                <Copy className="w-4 h-4 mr-1" />
                复制
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadContent(selectedContent)}
              >
                <Download className="w-4 h-4 mr-1" />
                下载
              </Button>
            </div>
          </div>
          
          <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown>{selectedContent}</ReactMarkdown>
            </div>
          </div>
        </motion.div>
      )}

      {/* 历史记录 */}
      <AnimatePresence>
        {showHistory && generatedContent.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t pt-6"
          >
            <h4 className="text-md font-medium text-gray-900 mb-4">历史记录</h4>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {generatedContent.slice(0, 5).map((content) => (
                <div
                  key={content.id}
                  className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => setSelectedContent(content.content)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-xs text-gray-500 mb-1">
                        {content.timestamp.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-700 truncate">
                        {content.prompt}
                      </p>
                    </div>
                    <div className="flex space-x-1 ml-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          regenerateContent(content);
                        }}
                        className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                        disabled={isGenerating}
                      >
                        <RefreshCw className="w-3 h-3" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          copyToClipboard(content.content);
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ContentGenerator;