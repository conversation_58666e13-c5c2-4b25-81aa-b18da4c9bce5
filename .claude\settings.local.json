{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npm run server:*)", "Bash(npm run dev:*)", "Bash(npm install)", "Bash(start /B npm run server)", "Bash(npm install:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(wmic process where processid=34352 delete:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx kill-port:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(start-docker.bat)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(npm run build:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker exec:*)"], "deny": []}}