-- PostgreSQL初始化脚本
-- 创建数据库和用户
CREATE USER aiuser WITH PASSWORD 'password123';
GRANT ALL PRIVILEGES ON DATABASE ai_toubiao TO aiuser;

-- 连接到ai_toubiao数据库
\c ai_toubiao;

-- 授权给用户
GRANT ALL ON SCHEMA public TO aiuser;

-- 创建用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    type VARCHAR(20) DEFAULT 'bidding',
    user_id INTEGER REFERENCES users(id),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文档表
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id),
    name VARCHAR(200) NOT NULL,
    type VARCHAR(50),
    content TEXT,
    metadata JSONB,
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建文件关联表
CREATE TABLE file_associations (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(20) NOT NULL, -- 'project', 'document', 'template'
    entity_id INTEGER NOT NULL,
    file_id VARCHAR(50) NOT NULL,
    file_name VARCHAR(200),
    file_path VARCHAR(500),
    relationship_type VARCHAR(20) DEFAULT 'attachment',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建模板表
CREATE TABLE templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    content JSONB,
    is_public BOOLEAN DEFAULT false,
    user_id INTEGER REFERENCES users(id),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建审计日志表
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    action VARCHAR(50) NOT NULL,
    entity_type VARCHAR(20),
    entity_id INTEGER,
    changes JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_at ON projects(created_at);

CREATE INDEX idx_documents_project_id ON documents(project_id);
CREATE INDEX idx_documents_type ON documents(type);
CREATE INDEX idx_documents_is_active ON documents(is_active);

CREATE INDEX idx_file_associations_entity ON file_associations(entity_type, entity_id);
CREATE INDEX idx_file_associations_file_id ON file_associations(file_id);

CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_is_public ON templates(is_public);
CREATE INDEX idx_templates_user_id ON templates(user_id);

CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始数据
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$10$hash', 'admin'),
('demo', '<EMAIL>', '$2b$10$hash', 'user');

INSERT INTO templates (name, category, description, content, is_public) VALUES
('标准技术标', 'technical', '通用技术标书模板', 
 '{"sections": [
   {"id": "overview", "name": "技术方案概述", "required": true},
   {"id": "solution", "name": "技术解决方案", "required": true},
   {"id": "implementation", "name": "实施计划", "required": true}
 ]}', true),
('标准商务标', 'commercial', '通用商务标书模板',
 '{"sections": [
   {"id": "pricing", "name": "报价策略", "required": true},
   {"id": "costs", "name": "成本分解", "required": true},
   {"id": "payment", "name": "付款条件", "required": true}
 ]}', true);

-- 授权表权限给用户
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aiuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aiuser;

SELECT 'PostgreSQL初始化完成' as status;