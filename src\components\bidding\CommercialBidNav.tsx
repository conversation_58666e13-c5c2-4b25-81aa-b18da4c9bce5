import React from 'react';
import { NavLink } from 'react-router-dom';
import { LayoutDashboard, Tags, Calculator, CreditCard, Contact as FileContract } from 'lucide-react';
import Card from '../ui/Card';

const CommercialBidNav: React.FC = () => {
  const navItems = [
    { path: '/commercial', label: '商务标概览', icon: <LayoutDashboard size={18} /> },
    { path: '/commercial/pricing', label: '报价策略', icon: <Tags size={18} /> },
    { path: '/commercial/costs', label: '成本明细', icon: <Calculator size={18} /> },
    { path: '/commercial/payment', label: '付款条件', icon: <CreditCard size={18} /> },
    { path: '/commercial/contract', label: '合同条款', icon: <FileContract size={18} /> },
  ];

  return (
    <Card className="overflow-hidden p-0">
      <div className="bg-gray-50 px-4 py-3 border-b">
        <h3 className="text-sm font-medium text-gray-700">商务标章节</h3>
      </div>
      <nav className="p-2">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                end={item.path === '/commercial'}
                className={({ isActive }) =>
                  `flex items-center px-3 py-2 text-sm rounded-md transition-colors
                  ${isActive 
                    ? 'bg-blue-50 text-blue-700 font-medium' 
                    : 'text-gray-700 hover:bg-gray-100'}`
                }
              >
                <span className="mr-3">{item.icon}</span>
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </Card>
  );
};

export default CommercialBidNav;