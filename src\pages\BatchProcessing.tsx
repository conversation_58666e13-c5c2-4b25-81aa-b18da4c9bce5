import React from 'react';
import BatchProcessor from '../components/batch/BatchProcessor';

const BatchProcessing: React.FC = () => {
  const handleBatchComplete = (results: any[]) => {
    console.log('批处理完成:', results);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">批量处理</h1>
        <p className="text-gray-500">批量分析和处理招标文件，提高工作效率</p>
      </div>
      
      <BatchProcessor onComplete={handleBatchComplete} />
    </div>
  );
};

export default BatchProcessing;