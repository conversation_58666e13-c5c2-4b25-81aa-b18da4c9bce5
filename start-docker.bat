@echo off
chcp 65001 >nul
echo 🚀 启动AI投标文件制作平台...

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建必要的目录...
if not exist "uploads" mkdir uploads
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "docker\nginx\ssl" mkdir docker\nginx\ssl

REM 停止现有服务
echo 🛑 停止现有服务...
docker-compose down

REM 询问是否清理旧镜像
set /p cleanup="是否清理旧的Docker镜像？(y/N): "
if /i "%cleanup%"=="y" (
    echo 🧹 清理旧镜像...
    docker system prune -f
    docker-compose build --no-cache
)

REM 启动服务
echo 🎯 启动所有服务...
docker-compose up -d

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo 📊 检查服务状态...
docker-compose ps

REM 显示服务信息
echo.
echo ✅ 服务启动完成！
echo.
echo 🌐 访问地址：
echo   主应用(Nginx): http://localhost:8080
echo   主应用(直接): http://localhost:3010
echo   API健康检查: http://localhost:3010/api/health
echo.
echo 🗄️ 数据库访问：
echo   MongoDB: localhost:27020 (admin/password123)
echo   Neo4j: http://localhost:7480 (neo4j/password123)
echo   PostgreSQL: localhost:5435 (postgres/password123)
echo   Redis: localhost:6385 (password123)
echo.
echo 📱 管理工具：
echo   Neo4j Browser: http://localhost:7480
echo.
echo 📋 常用命令：
echo   查看日志: docker-compose logs -f
echo   停止服务: docker-compose down
echo   重启服务: docker-compose restart
echo   进入容器: docker-compose exec app sh
echo.

REM 健康检查
echo 🔍 执行健康检查...
timeout /t 5 /nobreak >nul

curl -f http://localhost:3010/api/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 应用健康检查失败，请检查日志：
    echo    docker-compose logs app
) else (
    echo ✅ 应用健康检查通过
)

echo.
echo 🎉 AI投标文件制作平台已启动完成！
echo.
echo 按任意键继续...
pause >nul